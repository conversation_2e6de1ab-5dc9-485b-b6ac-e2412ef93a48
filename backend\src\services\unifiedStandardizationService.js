/**
 * Service unifié de standardisation
 * Ce service centralise toutes les fonctionnalités de standardisation entre le frontend et le backend
 */

const mongoose = require('mongoose');
const visitedObjects = new WeakSet();
// Constantes pour les noms de champs standardisés
const FIELD_CONSTANTS = {
  // Champs communs
  ID: 'id',
  MONGO_ID: '_id',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',

  // Champs de validation
  IS_VALID: 'isValid',
  IS_VALIDATED: 'isValidated',
  IS_BLOCKED: 'isBlocked',
  STATUS: 'status',

  // Champs utilisateur
  EMAIL: 'email',
  PASSWORD: 'password',
  NOM: 'nom',
  PRENOM: 'prenom',
  TELEPHONE: 'telephone',
  PHOTO: 'photo',
  ROLES: 'roles',
  USER_TYPE: 'userType',

  // Champs de référence
  PECHEUR_ID: 'pecheurId',
  PECHEUR: 'pecheur',
  VETERINAIRE_ID: 'veterinaireId',
  VETERINAIRE: 'veterinaire',
  MARYEUR_ID: 'maryeurId',
  MARYEUR: 'maryeur',
  CLIENT_ID: 'clientId',
  CLIENT: 'client',
  ACHETEUR_ID: 'acheteurId',
  ACHETEUR: 'acheteur',
  USER_ID: 'userId',
  USER: 'user',

  // Champs de lot
  IDENTIFIANT: 'identifiant',
  QUANTITE: 'quantite',
  POIDS: 'poids',
  TEMPERATURE: 'temperature',
  DATE_TEST: 'dateTest',
  TEST: 'test',
  VENDU: 'vendu',
  PRISE_ID: 'priseId',
  PRISE: 'prise',
  DATE_SOUMISSION: 'dateSoumission',
  PRIX_INITIAL: 'prixInitial',
  PRIX_MINIMAL: 'prixMinimal',
  PRIX_FINAL: 'prixFinal',
  PRIX_ACTUEL: 'prixActuel',
  IS_AUCTION_ACTIVE: 'isAuctionActive',
  DATE_FIN_ENCHERE: 'dateFinEnchere',

  // Champs d'espèce
  ESPECE_ID: 'especeId',
  ESPECE: 'espece',
  ESPECE_NOM: 'especeNom',
  DESCRIPTION: 'description',
  NOM_SCIENTIFIQUE: 'nomScientifique',
  PRIX_MOYEN: 'prixMoyen',
  IS_ACTIVE: 'isActive',
  CONFIANCE: 'confiance',
  SOURCE: 'source',
  ALTERNATIVES: 'alternatives',
  SAISON: 'saison',
  HABITAT: 'habitat',
  METHODE_PECHE: 'methodePeche',

  // Champs de prise
  DATE: 'date',
  LIEU: 'lieu',
  LATITUDE: 'latitude',
  LONGITUDE: 'longitude',
  DEBUT: 'debut',
  FIN: 'fin',
  ENGIN: 'engin',
  ZONE: 'zone',
  AFFECTATION_DATE: 'affectationDate',
  DATE_DEBARQUEMENT: 'dateDebarquement',

  // Champs d'image
  IMAGE_URL: 'imageUrl'
};

// Mappings de noms de champs non standards vers standards
const fieldMappings = {
  '_id': FIELD_CONSTANTS.ID,
  'is_valid': FIELD_CONSTANTS.IS_VALIDATED,
  'is_validated': FIELD_CONSTANTS.IS_VALIDATED,
  'isValidated': FIELD_CONSTANTS.IS_VALIDATED,
  'status': FIELD_CONSTANTS.IS_VALIDATED,
  'is_blocked': FIELD_CONSTANTS.IS_BLOCKED,
  'poid': FIELD_CONSTANTS.POIDS,
  'datetest': FIELD_CONSTANTS.DATE_TEST,
  'date_test': FIELD_CONSTANTS.DATE_TEST,
  'datesoumettre': FIELD_CONSTANTS.DATE_SOUMISSION,
  'date_soumission': FIELD_CONSTANTS.DATE_SOUMISSION,
  'prixinitial': FIELD_CONSTANTS.PRIX_INITIAL,
  'prix_initial': FIELD_CONSTANTS.PRIX_INITIAL,
  'prixminimal': FIELD_CONSTANTS.PRIX_MINIMAL,
  'prix_minimal': FIELD_CONSTANTS.PRIX_MINIMAL,
  'prixfinale': FIELD_CONSTANTS.PRIX_FINAL,
  'prix_final': FIELD_CONSTANTS.PRIX_FINAL,
  'vendre': FIELD_CONSTANTS.VENDU,
  'is_vendu': FIELD_CONSTANTS.VENDU,
  'prise_id': FIELD_CONSTANTS.PRISE_ID,
  'user_id': FIELD_CONSTANTS.USER_ID,
  'pecheur_id': FIELD_CONSTANTS.PECHEUR_ID,
  'veterinaire_id': FIELD_CONSTANTS.VETERINAIRE_ID,
  'vitirinaire': FIELD_CONSTANTS.VETERINAIRE,
  'vitirinaire_id': FIELD_CONSTANTS.VETERINAIRE_ID,
  'maryeur_id': FIELD_CONSTANTS.MARYEUR_ID,
  'acheteur_id': FIELD_CONSTANTS.ACHETEUR_ID,
  'created_at': FIELD_CONSTANTS.CREATED_AT,
  'updated_at': FIELD_CONSTANTS.UPDATED_AT,
  'date_creation': FIELD_CONSTANTS.CREATED_AT,
  'date_validation': FIELD_CONSTANTS.UPDATED_AT,
  'date_fin_enchere': FIELD_CONSTANTS.DATE_FIN_ENCHERE,
  'is_auction_active': FIELD_CONSTANTS.IS_AUCTION_ACTIVE,
  'enchereActive': FIELD_CONSTANTS.IS_AUCTION_ACTIVE,
  'prix_depart': FIELD_CONSTANTS.PRIX_INITIAL,
  'prix_actuel': FIELD_CONSTANTS.PRIX_ACTUEL,
  'image_url': FIELD_CONSTANTS.IMAGE_URL,
  'nom_scientifique': FIELD_CONSTANTS.NOM_SCIENTIFIQUE,
  'prix_moyen': FIELD_CONSTANTS.PRIX_MOYEN,
  'is_active': FIELD_CONSTANTS.IS_ACTIVE,
  'espece_nom': FIELD_CONSTANTS.ESPECE_NOM,
  'espece_id': FIELD_CONSTANTS.ESPECE_ID,
};

// Champs booléens
const booleanFields = [
  FIELD_CONSTANTS.IS_VALID,
  FIELD_CONSTANTS.IS_VALIDATED,
  FIELD_CONSTANTS.IS_BLOCKED,
  FIELD_CONSTANTS.STATUS,
  FIELD_CONSTANTS.TEST,
  FIELD_CONSTANTS.VENDU,
  FIELD_CONSTANTS.IS_AUCTION_ACTIVE,
  FIELD_CONSTANTS.IS_ACTIVE
];

// Champs de date
const dateFields = [
  FIELD_CONSTANTS.CREATED_AT,
  FIELD_CONSTANTS.UPDATED_AT,
  FIELD_CONSTANTS.DATE_TEST,
  FIELD_CONSTANTS.DATE_SOUMISSION,
  FIELD_CONSTANTS.DATE_FIN_ENCHERE,
  FIELD_CONSTANTS.DATE,
  FIELD_CONSTANTS.DEBUT,
  FIELD_CONSTANTS.FIN,
  FIELD_CONSTANTS.AFFECTATION_DATE,
  FIELD_CONSTANTS.DATE_DEBARQUEMENT
];

// Champs numériques
const numberFields = [
  FIELD_CONSTANTS.QUANTITE,
  FIELD_CONSTANTS.POIDS,
  FIELD_CONSTANTS.TEMPERATURE,
  FIELD_CONSTANTS.PRIX_INITIAL,
  FIELD_CONSTANTS.PRIX_MINIMAL,
  FIELD_CONSTANTS.PRIX_FINAL,
  FIELD_CONSTANTS.PRIX_ACTUEL,
  FIELD_CONSTANTS.PRIX_MOYEN,
  FIELD_CONSTANTS.LATITUDE,
  FIELD_CONSTANTS.LONGITUDE,
  FIELD_CONSTANTS.CONFIANCE
];

/**
 * Standardise les noms de champs dans un objet
 * @param {Object} obj - L'objet à standardiser
 * @param {Set} visited - Set pour éviter les références circulaires
 * @returns {Object} - L'objet avec des noms de champs standardisés
 */
function standardizeFieldNames(obj, visited = new Set()) {
  if (!obj || typeof obj !== 'object') return obj;

  // Éviter les références circulaires
  if (visited.has(obj)) return obj;
  visited.add(obj);

  const result = {};

  // Copier les champs originaux
  Object.keys(obj).forEach(key => {
    const value = obj[key];

    // Standardiser le nom du champ si nécessaire
    const standardKey = fieldMappings[key] || key;

    // Traiter récursivement les objets imbriqués (sauf les ObjectId MongoDB)
    if (value && typeof value === 'object' && !Array.isArray(value) &&
        !(value instanceof mongoose.Types.ObjectId) &&
        (!value.constructor || value.constructor.name !== 'ObjectId')) {
      result[standardKey] = standardizeFieldNames(value, visited);
    }
    // Traiter récursivement les tableaux d'objets
    else if (Array.isArray(value)) {
      result[standardKey] = value.map(item =>
        typeof item === 'object' && item !== null &&
        !(item instanceof mongoose.Types.ObjectId)
          ? standardizeFieldNames(item, visited)
          : item
      );
    }
    // Copier les valeurs simples et les ObjectId
    else {
      result[standardKey] = value;
    }
  });

  visited.delete(obj);
  return result;
}

/**
 * Standardise les types de données dans un objet
 * @param {Object} obj - L'objet à standardiser
 * @returns {Object} - L'objet avec des types de données standardisés
 */
function standardizeDataTypes(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  if (visitedObjects.has(obj)) return obj; // prevent infinite recursion

  visitedObjects.add(obj);
  const result = { ...obj };

  Object.keys(result).forEach(key => {
    const value = result[key];

    // Standardiser les booléens
    if (booleanFields.includes(key)) {
      if (value === 1 || value === '1' || value === 'true') {
        result[key] = true;
      } else if (value === 0 || value === '0' || value === 'false') {
        result[key] = false;
      }
    }

    // Standardiser les dates
    else if (dateFields.includes(key) && value) {
      if (typeof value === 'string' && !isNaN(Date.parse(value))) {
        result[key] = new Date(value);
      }
    }

    // Standardiser les nombres
    else if (numberFields.includes(key) && value) {
      if (typeof value === 'string' && !isNaN(parseFloat(value))) {
        result[key] = parseFloat(value);
      }
    }

    // Traiter récursivement les objets imbriqués
    else if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = standardizeDataTypes(value);
    }

    // Traiter récursivement les tableaux d'objets
    else if (Array.isArray(value)) {
      result[key] = value.map(item =>
        typeof item === 'object' && item !== null
          ? standardizeDataTypes(item)
          : item
      );
    }
  });

  return result;
}
/*
function standardizeDataTypes(obj) {
  if (!obj || typeof obj !== 'object') return obj;

  const result = { ...obj };

  // Standardiser les types de données
  Object.keys(result).forEach(key => {
    const value = result[key];

    // Standardiser les booléens
    if (booleanFields.includes(key)) {
      if (value === 1 || value === '1' || value === 'true') {
        result[key] = true;
      } else if (value === 0 || value === '0' || value === 'false') {
        result[key] = false;
      }
    }

    // Standardiser les dates
    else if (dateFields.includes(key) && value) {
      if (typeof value === 'string' && !isNaN(Date.parse(value))) {
        result[key] = new Date(value);
      }
    }

    // Standardiser les nombres
    else if (numberFields.includes(key) && value) {
      if (typeof value === 'string' && !isNaN(parseFloat(value))) {
        result[key] = parseFloat(value);
      }
    }

    // Traiter récursivement les objets imbriqués
    else if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = standardizeDataTypes(value);
    }

    // Traiter récursivement les tableaux d'objets
    else if (Array.isArray(value)) {
      result[key] = value.map(item =>
        typeof item === 'object' && item !== null
          ? standardizeDataTypes(item)
          : item
      );
    }
  });

  return result;
}*/

/**
 * Standardise les IDs dans un objet
 * @param {Object} obj - L'objet à standardiser
 * @param {WeakSet<Object>} [visited] - Ensemble des objets déjà visités
 * @returns {Object} - L'objet avec des IDs standardisés
 */
function standardizeIds(obj, visited = new WeakSet()) {
  if (!obj || typeof obj !== 'object') return obj;

  // Éviter les références circulaires
  if (visited.has(obj)) return obj;
  visited.add(obj);

  // Si c'est un tableau, standardiser chaque élément
  if (Array.isArray(obj)) {
    return obj.map(item => standardizeIds(item, visited));
  }

  const result = { ...obj };

  // Ajouter l'ID MongoDB comme ID si nécessaire
  if (result[FIELD_CONSTANTS.MONGO_ID] && !result[FIELD_CONSTANTS.ID]) {
    const id = result[FIELD_CONSTANTS.MONGO_ID];
    result[FIELD_CONSTANTS.ID] = id instanceof mongoose.Types.ObjectId ? id.toString() : id;
  }

  // Standardiser les IDs dans les objets imbriqués
  Object.keys(result).forEach(key => {
    const value = result[key];
    if (value && typeof value === 'object') {
      result[key] = standardizeIds(value, visited);
    }
  });

  return result;
}
/**
 * Standardise un objet pour qu'il soit compatible avec le frontend
 * @param {Object} obj - L'objet à standardiser
 * @returns {Object} - L'objet standardisé
 */
function standardizeForFrontend(obj) {
  if (!obj) return obj;

  // Standardiser les noms de champs, les types de données et les IDs
  return standardizeIds(standardizeDataTypes(standardizeFieldNames(obj)));
}

module.exports = {
  FIELD_CONSTANTS,
  standardizeFieldNames,
  standardizeDataTypes,
  standardizeIds,
  standardizeForFrontend
};
