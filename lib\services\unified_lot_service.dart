import 'package:flutter/foundation.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/client.dart';
import 'package:seatrace/models/base_user.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_notification_service.dart';
import 'package:seatrace/services/model_service.dart';
import 'package:seatrace/utils/error_handler.dart';

/// Service unifié pour la gestion des lots et des enchères
class UnifiedLotService {
  static final UnifiedLotService _instance = UnifiedLotService._internal();
  factory UnifiedLotService() => _instance;
  UnifiedLotService._internal();

  final UnifiedApiService _apiService = UnifiedApiService();
  final UnifiedAuthService _authService = UnifiedAuthService();
  final UnifiedNotificationService _notificationService =
      UnifiedNotificationService();
  final ModelService _modelService = ModelService();

  /// Méthode utilitaire pour vérifier l'utilisateur connecté et son type
  /// Retourne l'utilisateur si connecté et du bon type, sinon lance une exception
  Future<T> _checkUserType<T extends BaseUser>(String methodName) async {
    final user = await _authService.getCurrentUser();
    if (user == null) {
      throw Exception('Utilisateur non connecté');
    }

    if (user is! T) {
      final expectedType = T.toString();
      throw Exception('L\'utilisateur n\'est pas un $expectedType');
    }

    return user;
  }

  /// Méthode utilitaire pour convertir une réponse API en liste de lots
  List<Lot> _convertResponseToLots(Map<String, dynamic> response) {
    // Ajouter un log pour voir le type de données reçu
    ErrorHandler.instance.logInfo(
      'Type de données reçu dans _convertResponseToLots: ${response['data']?.runtimeType}',
      context: 'UnifiedLotService._convertResponseToLots',
    );

    if (response.containsKey('data')) {
      if (response['data'] is List) {
        // Si c'est une liste, convertir chaque élément en Lot
        final List<dynamic> lotsData = response['data'];
        return lotsData
            .map(
              (lotData) => Lot.fromMap(_modelService.standardizeMap(lotData)),
            )
            .toList();
      } else if (response['data'] is Map) {
        // Si c'est un Map, le convertir en un seul Lot dans une liste
        try {
          final lot = Lot.fromMap(
            _modelService.standardizeMap(response['data']),
          );
          return [lot];
        } catch (e) {
          ErrorHandler.instance.logError(
            'Erreur lors de la conversion du Map en Lot: $e',
            context: 'UnifiedLotService._convertResponseToLots',
          );
        }
      }
    }

    // Si aucune donnée valide n'est trouvée, retourner une liste vide
    return [];
  }

  /// Méthode utilitaire pour exécuter une opération avec gestion d'erreur
  ///
  /// Cette méthode exécute une fonction asynchrone et gère les erreurs de manière cohérente.
  /// Elle journalise l'erreur avec le contexte fourni et peut soit propager l'erreur,
  /// soit retourner une valeur par défaut en cas d'erreur.
  ///
  /// @param operation La fonction à exécuter
  /// @param context Le contexte pour la journalisation des erreurs
  /// @param propagateError Si true, propage l'erreur, sinon retourne defaultValue
  /// @param defaultValue La valeur à retourner en cas d'erreur si propagateError est false
  /// @return Le résultat de l'opération ou defaultValue en cas d'erreur
  Future<T> _executeWithErrorHandling<T>({
    required Future<T> Function() operation,
    required String context,
    bool propagateError = false,
    T? defaultValue,
  }) async {
    try {
      return await operation();
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedLotService.$context');

      if (propagateError) {
        throw Exception('Erreur dans $context: $e');
      }

      return defaultValue as T;
    }
  }

  /// Méthode utilitaire pour envoyer une notification
  Future<void> _sendNotification({
    required String destinataireId,
    required String destinataireType,
    required String titre,
    required String contenu,
    required String type,
    required String referenceId,
    String referenceType = 'Lot',
  }) async {
    await _executeWithErrorHandling(
      operation:
          () => _notificationService.createNotification(
            destinataireId: destinataireId,
            destinataireType: destinataireType,
            titre: titre,
            contenu: contenu,
            type: type,
            referenceId: referenceId,
            referenceType: referenceType,
          ),
      context: '_sendNotification',
      // Ne pas propager l'erreur pour ne pas bloquer l'exécution si la notification échoue
      propagateError: false,
      defaultValue: null,
    );
  }

  /// Récupérer les lots en attente pour un vétérinaire
  Future<List<Lot>> getPendingLots() async {
    return await _executeWithErrorHandling(
      operation: () async {
        // Vérifier que l'utilisateur est un vétérinaire
        await _checkUserType<Veterinaire>('getPendingLots');

        final response = await _apiService.get('lots/pending');
        return _convertResponseToLots(response);
      },
      context: 'getPendingLots',
      defaultValue: <Lot>[],
    );
  }

  /// Approuver un lot
  ///
  /// Cette méthode permet à un vétérinaire d'approuver un lot.
  /// Elle vérifie que l'utilisateur est connecté et est un vétérinaire,
  /// puis envoie une requête au serveur pour approuver le lot.
  /// Elle notifie également le pêcheur et le mareyeur de l'approbation.
  ///
  /// @param lotId L'ID du lot à approuver
  /// @return true si l'approbation a réussi, false sinon
  Future<bool> approveLot(String lotId) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Veterinaire>('approveLot');

        // Récupérer les détails du lot avant approbation
        final lotBefore = await getLotById(lotId);
        if (lotBefore == null) {
          throw Exception('Lot non trouvé');
        }

        // Envoyer la requête d'approbation
        final response = await _apiService.put('lots/$lotId/approve', {
          'veterinaireId': user.id,
          'temperature': 4.0, // Température par défaut
          if (lotBefore.maryeurId != null) 'maryeur': lotBefore.maryeurId,
        });

        final success =
            response.containsKey('success') ? response['success'] : true;

        if (success) {
          await _sendApprovalNotifications(lotId, lotBefore, user);
        }

        return success;
      },
      context: 'approveLot',
      defaultValue: false,
    );
  }

  /// Envoie les notifications après l'approbation d'un lot
  Future<void> _sendApprovalNotifications(
    String lotId,
    Lot lotBefore,
    Veterinaire user,
  ) async {
    // Récupérer le lot mis à jour
    final lot = await getLotById(lotId);
    if (lot != null) {
      // Notifier le pêcheur
      await _sendNotification(
        destinataireId: lot.userId,
        destinataireType: 'Pecheur',
        titre: 'Lot approuvé',
        contenu:
            'Votre lot de ${lot.especeNom} a été approuvé par le vétérinaire ${user.prenom} ${user.nom} et transmis au mareyeur',
        type: 'success',
        referenceId: lotId,
      );

      // Notifier le mareyeur
      final maryeurId = lot.maryeurId ?? lotBefore.maryeurId;
      if (maryeurId != null) {
        await _sendNotification(
          destinataireId: maryeurId,
          destinataireType: 'Maryeur',
          titre: 'Nouveau lot disponible',
          contenu:
              'Un lot de ${lot.especeNom} a été approuvé par le vétérinaire ${user.prenom} ${user.nom} et est prêt pour la mise en vente',
          type: 'info',
          referenceId: lotId,
        );
      }
    }
  }

  /// Rejeter un lot
  Future<bool> rejectLot(String lotId, {String? motif}) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Veterinaire>('rejectLot');

        // Récupérer les détails du lot avant rejet
        final lotBefore = await getLotById(lotId);
        if (lotBefore == null) {
          throw Exception('Lot non trouvé');
        }

        // Envoyer la requête de rejet
        final response = await _apiService.put('lots/$lotId/reject', {
          'veterinaireId': user.id,
          'temperature': 4.0, // Température par défaut
          'motif': motif,
          if (lotBefore.maryeurId != null) 'maryeur': lotBefore.maryeurId,
        });

        final success =
            response.containsKey('success') ? response['success'] : true;

        if (success) {
          await _sendRejectionNotifications(lotId, lotBefore, user, motif);
        }

        return success;
      },
      context: 'rejectLot',
      defaultValue: false,
    );
  }

  /// Envoie les notifications après le rejet d'un lot
  Future<void> _sendRejectionNotifications(
    String lotId,
    Lot lotBefore,
    Veterinaire user,
    String? motif,
  ) async {
    // Récupérer le lot mis à jour
    final lot = await getLotById(lotId);
    if (lot != null) {
      final motifText = motif != null ? '. Motif: $motif' : '';

      // Notifier le pêcheur
      await _sendNotification(
        destinataireId: lot.userId,
        destinataireType: 'Pecheur',
        titre: 'Lot refusé',
        contenu:
            'Votre lot de ${lot.especeNom} a été refusé par le vétérinaire ${user.prenom} ${user.nom}$motifText',
        type: 'error',
        referenceId: lotId,
      );

      // Notifier le mareyeur
      final maryeurId = lot.maryeurId ?? lotBefore.maryeurId;
      if (maryeurId != null) {
        await _sendNotification(
          destinataireId: maryeurId,
          destinataireType: 'Maryeur',
          titre: 'Lot refusé par le vétérinaire',
          contenu:
              'Le lot de ${lot.especeNom} a été refusé par le vétérinaire ${user.prenom} ${user.nom}$motifText',
          type: 'warning',
          referenceId: lotId,
        );
      }
    }
  }

  /// Définit le prix d'un lot (pour les maryeurs)
  Future<bool> setPriceLot(String lotId, double prixMinimal) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Maryeur>('setPriceLot');

        // Définir le prix
        await _apiService.put('lots/$lotId/set-price', {
          'prixMinimal': prixMinimal,
          'prixInitial': prixMinimal,
          'typeEnchere': 'standard',
          'online': true,
        });

        // Notifier le pêcheur
        await _notifyPriceSet(lotId, prixMinimal, user);

        return true;
      },
      context: 'setPriceLot',
      defaultValue: false,
    );
  }

  /// Notifie le pêcheur que le prix a été défini
  Future<void> _notifyPriceSet(
    String lotId,
    double prixMinimal,
    Maryeur user,
  ) async {
    final lot = await getLotById(lotId);
    if (lot != null) {
      await _sendNotification(
        destinataireId: lot.userId,
        destinataireType: 'Pecheur',
        titre: 'Prix défini pour votre lot',
        contenu:
            'Le maryeur ${user.prenom} ${user.nom} a défini un prix de $prixMinimal TND pour votre lot de ${lot.especeNom}',
        type: 'info',
        referenceId: lotId,
      );
    }
  }

  /// Récupérer les lots en attente de prix pour un maryeur
  Future<List<Lot>> getPendingPriceLots() async {
    return await _executeWithErrorHandling(
      operation: () async {
        // Vérifier que l'utilisateur est un maryeur
        await _checkUserType<Maryeur>('getPendingPriceLots');

        final response = await _apiService.get('lots/pending-price');
        return _convertResponseToLots(response);
      },
      context: 'getPendingPriceLots',
      defaultValue: <Lot>[],
    );
  }

  /// Récupérer les enchères actives pour un maryeur
  Future<List<Lot>> getActiveAuctions() async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Maryeur>('getActiveAuctions');

        final response = await _apiService.get(
          'lots/auctions/active/maryeur/${user.id}',
        );
        return _convertResponseToLots(response);
      },
      context: 'getActiveAuctions',
      defaultValue: <Lot>[],
    );
  }

  /// Récupérer les enchères disponibles pour un client
  Future<List<Lot>> getAvailableAuctions() async {
    return await _executeWithErrorHandling(
      operation: () async {
        final response = await _apiService.get('lots/auctions/available');
        return _convertResponseToLots(response);
      },
      context: 'getAvailableAuctions',
      defaultValue: <Lot>[],
    );
  }

  /// Récupérer les achats d'un client
  Future<List<Lot>> getMyPurchases() async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Client>('getMyPurchases');

        final response = await _apiService.get(
          'lots/purchases/client/${user.id}',
        );
        return _convertResponseToLots(response);
      },
      context: 'getMyPurchases',
      defaultValue: <Lot>[],
    );
  }

  /// Placer une enchère avec mise à jour en temps réel
  Future<bool> placeBid(
    String auctionId,
    double bidAmount,
    String userId,
  ) async {
    return await _executeWithErrorHandling(
      operation: () async {
        await _checkUserType<Client>('placeBid');

        final response = await _apiService.post('lots/$auctionId/bid', {
          'amount': bidAmount,
          'userId': userId,
        });

        return response.containsKey('success') ? response['success'] : true;
      },
      context: 'placeBid',
      defaultValue: false,
    );
  }

  /// Récupérer les détails d'une enchère
  Future<Lot?> getAuctionDetails(String auctionId) async {
    return await _executeWithErrorHandling(
      operation: () => getLotById(auctionId),
      context: 'getAuctionDetails',
      defaultValue: null,
    );
  }

  /// Récupérer un lot par son ID
  Future<Lot?> getLotById(String lotId) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final response = await _apiService.get('lots/$lotId');
        if (response.containsKey('data')) {
          return Lot.fromMap(_modelService.standardizeMap(response['data']));
        }
        return null;
      },
      context: 'getLotById',
      defaultValue: null,
    );
  }

  /// Convertit une Map en objet Lot
  Lot? getLotFromMap(Map<String, dynamic> map) {
    try {
      return Lot.fromMap(_modelService.standardizeMap(map));
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedLotService.getLotFromMap',
      );
      return null;
    }
  }

  /// Récupérer les lots d'un pêcheur
  Future<List<Lot>> getLotsByPecheur() async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Pecheur>('getLotsByPecheur');

        final response = await _apiService.get('lots/pecheur/${user.id}');
        return _convertResponseToLots(response);
      },
      context: 'getLotsByPecheur',
      defaultValue: <Lot>[],
    );
  }

  /// Créer un nouveau lot
  ///
  /// Cette méthode crée un nouveau lot à partir des données fournies.
  /// Elle vérifie que l'utilisateur est connecté et est un pêcheur,
  /// puis valide les données du lot avant de l'envoyer au serveur.
  ///
  /// @param lotData Les données du lot à créer
  /// @return Le lot créé ou null en cas d'erreur
  Future<Lot?> createLot(Map<String, dynamic> inputData) async {
    return await _executeWithErrorHandling(
      operation: () async {
        // Vérifier que les données du lot contiennent les champs requis
        if (inputData.isEmpty) {
          throw Exception('Les données du lot sont vides');
        }

        // Vérifier que l'utilisateur est connecté et est un pêcheur
        final user = await _checkUserType<Pecheur>('createLot');

        // Journaliser les données brutes pour le débogage
        debugPrint('Données brutes du lot: $inputData');

        // Vérifier les champs essentiels avant de continuer
        _validateEssentialFields(inputData, user);

        try {
          // Normaliser les données du lot
          final normalizedData = _normalizeLotData(inputData, user);

          // Ajouter des champs supplémentaires pour garantir la compatibilité
          normalizedData['status'] = false;
          normalizedData['test'] = false;
          normalizedData['vendu'] = false;
          normalizedData['isValidated'] = false;

          // S'assurer que l'identifiant est présent
          if (!normalizedData.containsKey('identifiant') ||
              normalizedData['identifiant'] == null) {
            normalizedData['identifiant'] =
                'LOT-${DateTime.now().millisecondsSinceEpoch}';
          }

          // Utiliser la route principale pour créer le lot
          debugPrint('Utilisation de la route principale pour créer le lot');

          // Préparer les données minimales nécessaires - sans référence à Espece ou Prise
          final simplifiedData = {
            // Informations de base
            'identifiant': normalizedData['identifiant'],
            'photo': normalizedData['photo'] ?? normalizedData['imageUrl'],
            'especeNom':
                normalizedData['especeNom'] ??
                normalizedData['espece'] ??
                'Poisson',
            'poids': normalizedData['poids'] ?? 0,
            'temperature': normalizedData['temperature'] ?? 0,
            'lieu':
                normalizedData['lieu'] ??
                normalizedData['emplacement'] ??
                'Non spécifié',
            'description': normalizedData['description'],

            // Acteurs concernés
            'user': user.id,
            'veterinaire': normalizedData['veterinaire'],
            'maryeur': normalizedData['maryeur'],

            // Statuts (obligatoires)
            'status': false,
            'test': false,
            'vendu': false,
            'isValidated': false,
          };

          // Journaliser les données simplifiées
          debugPrint('Données simplifiées du lot: $simplifiedData');

          // Envoyer la requête à la route principale
          final response = await _apiService.post('lots', simplifiedData);

          // Journaliser la réponse
          debugPrint('Réponse du serveur (route simplifiée): $response');

          // Vérifier si la réponse contient une erreur
          if (response.containsKey('error')) {
            debugPrint('Erreur du serveur: ${response['error']}');

            // Tentative de récupération - essayer une approche alternative
            debugPrint(
              'Tentative de récupération avec une approche alternative...',
            );

            // Simplifier les données et réessayer avec seulement les champs essentiels
            // Approche simplifiée: un lot représente directement une capture d'une seule espèce
            final simplifiedData = {
              // Identifiants et métadonnées
              'identifiant': normalizedData['identifiant'],
              'nom': normalizedData['nom'] ?? 'Lot de poisson',
              'dateSoumission': DateTime.now().toIso8601String(),

              // Informations sur le poisson
              'photo': normalizedData['photo'] ?? '',
              'poids': normalizedData['poids'] ?? 1.0,
              'temperature': normalizedData['temperature'] ?? 4.0,
              'quantite': 1,

              // Informations sur l'espèce
              'espece': normalizedData['espece'],
              'especeNom': normalizedData['especeNom'] ?? 'Poisson',

              // Informations sur la pêche
              'lieu':
                  normalizedData['lieu'] ??
                  normalizedData['emplacement'] ??
                  'Non spécifié',

              // Acteurs concernés
              'user': user.id,
              'pecheur': user.id,
              'veterinaire': normalizedData['veterinaire'],
              'maryeur': normalizedData['maryeur'],

              // Statuts (obligatoires)
              'status': false,
              'test': false,
              'vendu': false,
              'isValidated': false,
            };

            // especeNom est déjà inclus dans les données simplifiées

            final retryResponse = await _apiService.post(
              'lots',
              simplifiedData,
            );

            if (retryResponse.containsKey('data')) {
              final lot = Lot.fromMap(retryResponse['data']);
              await _verifyAndFixLotAssociations(lot, normalizedData);
              await _sendLotCreationNotifications(lot, normalizedData, user);
              return lot;
            } else {
              throw Exception(
                'Erreur du serveur après tentative de récupération: ${retryResponse['error'] ?? 'Erreur inconnue'}',
              );
            }
          }

          // Vérifier si la réponse contient des données dans 'lot' ou 'data'
          Map<String, dynamic> lotData;
          if (response.containsKey('lot')) {
            lotData = response['lot'];
          } else if (response.containsKey('data')) {
            lotData = response['data'];
          } else {
            throw Exception(
              'Réponse inattendue du serveur: ni lot ni data trouvés',
            );
          }

          // Créer l'objet Lot à partir de la réponse
          final lot = Lot.fromMap(lotData);

          // Vérifier et corriger les associations si nécessaire
          await _verifyAndFixLotAssociations(lot, normalizedData);

          // Envoyer les notifications
          await _sendLotCreationNotifications(lot, normalizedData, user);

          return lot;
        } catch (e) {
          debugPrint('Erreur lors de la création du lot: $e');

          // Dernière tentative avec les données originales mais sans normalisation
          final directData = Map<String, dynamic>.from(inputData);

          if (!directData.containsKey('identifiant') ||
              directData['identifiant'] == null) {
            directData['identifiant'] =
                'LOT-${DateTime.now().millisecondsSinceEpoch}';
          }

          // S'assurer que l'utilisateur est inclus
          directData['user'] = user.id;
          directData['pecheur'] = user.id;

          // Ajouter les champs de statut
          directData['test'] = false;
          directData['isValidated'] = false;
          directData['status'] = false;
          directData['vendu'] = false;

          // Utiliser les données originales directement
          final lastResponse = await _apiService.post('lots', directData);

          if (lastResponse.containsKey('data')) {
            return Lot.fromMap(lastResponse['data']);
          }

          // Si toutes les tentatives échouent, propager l'erreur
          throw Exception(
            'Impossible de créer le lot après plusieurs tentatives',
          );
        }
      },
      context: 'createLot',
      defaultValue: null,
      propagateError:
          true, // Propager l'erreur pour que l'appelant puisse la gérer
    );
  }

  /// Normalise les données du lot avant l'envoi au serveur
  /// Approche simplifiée: un lot représente directement une capture d'une seule espèce
  Map<String, dynamic> _normalizeLotData(
    Map<String, dynamic> lotData,
    Pecheur user,
  ) {
    // Copier les données pour ne pas modifier l'original
    final normalizedData = Map<String, dynamic>.from(lotData);

    // Ajouter les champs d'identification du pêcheur
    normalizedData['pecheur'] = user.id;
    normalizedData['user'] = user.id;

    // Ajouter les champs de statut
    normalizedData['isValidated'] = false;
    normalizedData['status'] = false;
    normalizedData['test'] = false;
    normalizedData['vendu'] = false;

    // Ajouter un identifiant si manquant
    if (!normalizedData.containsKey('identifiant') ||
        normalizedData['identifiant'] == null) {
      normalizedData['identifiant'] =
          'LOT-${DateTime.now().millisecondsSinceEpoch}';
    }

    // Ajouter un nom si manquant
    if (!normalizedData.containsKey('nom') || normalizedData['nom'] == null) {
      String especeNom = 'poisson';
      if (normalizedData.containsKey('especeNom') &&
          normalizedData['especeNom'] != null) {
        especeNom = normalizedData['especeNom'];
      }
      normalizedData['nom'] = 'Lot de $especeNom';
    }

    // Normaliser les IDs essentiels (vétérinaire, mareyeur)
    final requiredFields = ['veterinaire', 'maryeur'];
    for (final field in requiredFields) {
      // Vérifier si l'ID est présent sous une forme ou une autre
      if (!normalizedData.containsKey(field) || normalizedData[field] == null) {
        final alternativeIdField = '${field}Id';
        if (normalizedData.containsKey(alternativeIdField) &&
            normalizedData[alternativeIdField] != null) {
          normalizedData[field] = normalizedData[alternativeIdField];
        } else {
          throw Exception('L\'ID du $field est requis pour créer un lot');
        }
      }

      // Normaliser l'ID
      normalizedData[field] = normalizedData[field].toString().trim();
      normalizedData['${field}Id'] = normalizedData[field];
    }

    // S'assurer que le nom de l'espèce est présent
    if (!normalizedData.containsKey('especeNom') ||
        normalizedData['especeNom'] == null) {
      // Si l'espèce est un objet, extraire son nom
      if (normalizedData.containsKey('espece') &&
          normalizedData['espece'] is Map) {
        normalizedData['especeNom'] =
            normalizedData['espece']['nom'] ?? 'Poisson';
      } else {
        normalizedData['especeNom'] = 'Poisson';
      }
    }

    // S'assurer que les champs obligatoires sont présents
    normalizedData['test'] = false;
    normalizedData['vendu'] = false;

    // Approche simplifiée: pas besoin de prise séparée
    // Si un lieu est fourni, l'utiliser directement dans le lot
    if (normalizedData.containsKey('lieu') && normalizedData['lieu'] != null) {
      // Le lieu est déjà défini, ne rien faire
    } else if (normalizedData.containsKey('emplacement') &&
        normalizedData['emplacement'] != null) {
      normalizedData['lieu'] = normalizedData['emplacement'];
    } else {
      normalizedData['lieu'] = 'Non spécifié';
    }

    // Journaliser les données normalisées pour le débogage
    debugPrint('Données normalisées du lot: $normalizedData');

    return normalizedData;
  }

  /// Vérifie et corrige les associations du lot si nécessaire
  Future<void> _verifyAndFixLotAssociations(
    Lot lot,
    Map<String, dynamic> lotData,
  ) async {
    // Vérifier l'association avec le vétérinaire
    if (lot.veterinaireId == null || lot.veterinaireId!.isEmpty) {
      final vetId = lotData['veterinaire'].toString();
      debugPrint(
        'Correction de l\'association vétérinaire: $vetId pour le lot ${lot.id}',
      );
      final vetResponse = await _executeWithErrorHandling(
        operation:
            () => _apiService.patch('lots/${lot.id}', {
              'veterinaire': vetId,
              'veterinaireId': vetId,
            }),
        context: '_verifyAndFixLotAssociations_veterinaire',
        propagateError: false,
      );
      debugPrint('Réponse de correction vétérinaire: $vetResponse');
    }

    // Vérifier l'association avec le mareyeur
    if (lot.maryeurId == null || lot.maryeurId!.isEmpty) {
      final maryeurId = lotData['maryeur'].toString();
      debugPrint(
        'Correction de l\'association mareyeur: $maryeurId pour le lot ${lot.id}',
      );
      final maryeurResponse = await _executeWithErrorHandling(
        operation:
            () => _apiService.patch('lots/${lot.id}', {
              'maryeur': maryeurId,
              'maryeurId': maryeurId,
            }),
        context: '_verifyAndFixLotAssociations_maryeur',
        propagateError: false,
      );
      debugPrint('Réponse de correction mareyeur: $maryeurResponse');
    }

    // Vérification finale des associations
    final lotFinal = await getLotById(lot.id);
    if (lotFinal != null) {
      debugPrint('Vérification finale des associations:');
      debugPrint('- Vétérinaire: ${lotFinal.veterinaireId}');
      debugPrint('- Mareyeur: ${lotFinal.maryeurId}');

      // Si les associations sont toujours manquantes, essayer une dernière fois avec une requête combinée
      if ((lotFinal.veterinaireId == null || lotFinal.veterinaireId!.isEmpty) ||
          (lotFinal.maryeurId == null || lotFinal.maryeurId!.isEmpty)) {
        debugPrint('Tentative finale de correction des associations');
        await _executeWithErrorHandling(
          operation:
              () => _apiService.patch('lots/${lot.id}', {
                'veterinaire': lotData['veterinaire'].toString(),
                'maryeur': lotData['maryeur'].toString(),
                'isValidated': false,
                'test': false,
                'vendu': false,
              }),
          context: '_verifyAndFixLotAssociations_final',
          propagateError: false,
        );
      }
    }
  }

  /// Valide les champs essentiels pour la création d'un lot - Version simplifiée
  void _validateEssentialFields(Map<String, dynamic> lotData, Pecheur user) {
    // Vérifier la présence du vétérinaire (champ obligatoire)
    if (!lotData.containsKey('veterinaire') || lotData['veterinaire'] == null) {
      if (!lotData.containsKey('veterinaireId') ||
          lotData['veterinaireId'] == null) {
        throw Exception('Vétérinaire requis pour créer un lot');
      }
    }

    // Vérifier la présence du mareyeur (champ obligatoire)
    if (!lotData.containsKey('maryeur') || lotData['maryeur'] == null) {
      if (!lotData.containsKey('maryeurId') || lotData['maryeurId'] == null) {
        throw Exception('Mareyeur requis pour créer un lot');
      }
    }

    // Vérifier la présence du nom de l'espèce (champ optionnel avec valeur par défaut)
    if (!lotData.containsKey('especeNom') || lotData['especeNom'] == null) {
      lotData['especeNom'] = 'Poisson';
    }

    // Ajouter des valeurs par défaut pour les champs non critiques
    if (!lotData.containsKey('poids') || lotData['poids'] == null) {
      lotData['poids'] = 1.0;
    }

    if (!lotData.containsKey('temperature') || lotData['temperature'] == null) {
      lotData['temperature'] = 4.0;
    }
  }

  /// Envoie les notifications après la création d'un lot
  Future<void> _sendLotCreationNotifications(
    Lot lot,
    Map<String, dynamic> lotData,
    Pecheur user,
  ) async {
    // Notification au vétérinaire
    final vetId = lotData['veterinaire'].toString();
    await _sendNotification(
      destinataireId: vetId,
      destinataireType: 'Veterinaire',
      titre: 'Nouveau lot à valider',
      contenu:
          'Un nouveau lot de ${lotData['especeNom'] ?? 'poisson'} vous a été assigné pour validation par le pêcheur ${user.prenom} ${user.nom}',
      type: 'info',
      referenceId: lot.id,
    );

    // Notification au pêcheur
    await _sendNotification(
      destinataireId: user.id.toString(),
      destinataireType: 'Pecheur',
      titre: 'Lot créé avec succès',
      contenu:
          'Votre lot de ${lotData['especeNom'] ?? 'poisson'} a été créé et envoyé au vétérinaire pour validation.',
      type: 'success',
      referenceId: lot.id,
    );
  }

  /// Définir le prix minimal pour un lot
  Future<bool> setMinPrice(String lotId, double minPrice) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Maryeur>('setMinPrice');

        await _apiService.put('lots/$lotId/price', {
          'prixMinimal': minPrice,
          'maryeur': user.id,
        });

        return true;
      },
      context: 'setMinPrice',
      defaultValue: false,
    );
  }

  /// Démarrer une enchère avec une durée fixe de 10 minutes
  Future<bool> startAuction(String lotId) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Maryeur>('startAuction');

        // L'API gère maintenant la durée fixe de 10 minutes
        await _apiService.put('lots/$lotId/auction/start', {
          'maryeur': user.id,
        });

        return true;
      },
      context: 'startAuction',
      defaultValue: false,
    );
  }

  /// Terminer une enchère
  Future<bool> endAuction(String lotId) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Maryeur>('endAuction');

        await _apiService.put('lots/$lotId/auction/end', {'maryeur': user.id});

        return true;
      },
      context: 'endAuction',
      defaultValue: false,
    );
  }

  /// Prolonger la durée d'une enchère
  Future<bool> extendAuction(String lotId, int additionalMinutes) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final user = await _checkUserType<Maryeur>('extendAuction');

        if (additionalMinutes <= 0) {
          throw Exception(
            'Le nombre de minutes supplémentaires doit être positif',
          );
        }

        await _apiService.put('lots/$lotId/auction/extend', {
          'maryeur': user.id,
          'additionalMinutes': additionalMinutes,
        });

        return true;
      },
      context: 'extendAuction',
      defaultValue: false,
    );
  }

  /// Récupère l'historique des validations d'un vétérinaire
  Future<List<Lot>> getVeterinaireHistory(
    String veterinaireId, {
    String? status,
  }) async {
    return await _executeWithErrorHandling(
      operation: () async {
        String url = 'lots/history/veterinaire/$veterinaireId';
        if (status != null) {
          url += '?status=$status';
        }
        final response = await _apiService.get(url);
        return _convertResponseToLots(response);
      },
      context: 'getVeterinaireHistory',
      defaultValue: <Lot>[],
    );
  }

  /// Récupère les enchères actives d'un mareyeur
  Future<List<Lot>> getMaryeurActiveAuctions(String maryeurId) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final response = await _apiService.get(
          'lots/auctions/maryeur/$maryeurId',
        );
        return _convertResponseToLots(response);
      },
      context: 'getMaryeurActiveAuctions',
      defaultValue: <Lot>[],
    );
  }

  /// Récupère les lots vendus d'un mareyeur
  Future<List<Lot>> getMaryeurSoldLots(String maryeurId) async {
    return await _executeWithErrorHandling(
      operation: () async {
        final response = await _apiService.get('lots/sold/maryeur/$maryeurId');
        return _convertResponseToLots(response);
      },
      context: 'getMaryeurSoldLots',
      defaultValue: <Lot>[],
    );
  }
}
