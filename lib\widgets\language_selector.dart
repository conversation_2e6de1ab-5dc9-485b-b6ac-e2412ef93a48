import 'package:flutter/material.dart';
import 'package:seatrace/services/language_service.dart';

/// Widget pour sélectionner la langue de l'application
class LanguageSelector extends StatelessWidget {
  final bool showLabel;
  final bool isCompact;
  
  const LanguageSelector({
    super.key,
    this.showLabel = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;
    final surfaceColor = theme.colorScheme.surface;
    
    return ValueListenableBuilder<Locale>(
      valueListenable: LanguageService().languageNotifier,
      builder: (context, locale, _) {
        return Container(
          decoration: BoxDecoration(
            color: surfaceColor.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(isCompact ? 16 : 20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: <PERSON>up<PERSON><PERSON>uButton<String>(
            icon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.language,
                  color: primaryColor,
                  size: isCompact ? 16 : 20,
                ),
                if (showLabel) ...[
                  const SizedBox(width: 4),
                  Text(
                    locale.languageCode.toUpperCase(),
                    style: TextStyle(
                      color: primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: isCompact ? 10 : 12,
                    ),
                  ),
                ],
              ],
            ),
            onSelected: (String languageCode) async {
              try {
                await LanguageService().setLanguage(languageCode);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        _getSuccessMessage(languageCode),
                      ),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'fr',
                child: Row(
                  children: [
                    Text('🇫🇷'),
                    SizedBox(width: 8),
                    Text('Français'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'ar',
                child: Row(
                  children: [
                    Text('🇹🇳'),
                    SizedBox(width: 8),
                    Text('العربية'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  String _getSuccessMessage(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return 'Langue changée en français';
      case 'ar':
        return 'تم تغيير اللغة إلى العربية';
      default:
        return 'Language changed';
    }
  }
}
