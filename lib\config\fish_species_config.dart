/// Configuration des espèces de poissons pour le système de classification
class FishSpeciesConfig {
  /// Liste des espèces de poissons prioritaires pour la reconnaissance
  /// Ces espèces sont les plus importantes à reconnaître correctement
  static const List<String> prioritySpecies = [
    'rouget',
    'dorade',
    'bar',
    'thon',
    'sardine',
    'anchois',
    'maquereau',
    'merlu',
    'sole',
    'espadon',
  ];

  /// Liste des espèces de poissons méditerranéens
  static const List<String> mediterraneanSpecies = [
    'baliste',
    'bou kachech',
    'boumessk',
    'bouri',
    'calamr',
    'chevrette',
    'choubay',
    'crevettes',
    'djej',
    'espadon',
    'far bhar',
    'ghzel',
    'jaghali',
    'kalb bhar',
    'karnit',
    'karous',
    'karradh',
    'khadhraya',
    'mankous',
    'mannani',
    'mbellem',
    'meeza',
    'morjan',
    'msalla',
    'sardouk',
    'sbares',
    'scorpaena',
    'serdina',
    'thon',
    'trillia',
    'wrata',
  ];

  /// Liste des espèces de poissons supplémentaires
  static const List<String> additionalSpecies = [
    'anchois',
    'bar',
    'baudroie',
    'bonite',
    'cabillaud',
    'congre',
    'daurade',
    'denté',
    'dorade',
    'grondin',
    'loup de mer',
    'maquereau',
    'merlan',
    'merlu',
    'mulet',
    'pageot',
    'pagre',
    'raie',
    'rascasse',
    'requin',
    'rouget',
    'saint-pierre',
    'sar',
    'sole',
    'turbot',
  ];

  /// Obtient la liste complète des espèces de poissons
  static List<String> getAllSpecies() {
    final Set<String> allSpecies = {
      ...mediterraneanSpecies,
      ...additionalSpecies,
    };
    return allSpecies.toList()..sort();
  }

  /// Mapping des noms d'espèces en anglais vers les noms en français
  /// Inclut de nombreuses variations et synonymes pour améliorer la reconnaissance
  static const Map<String, String> englishToFrench = {
    // Thon - Tuna
    'tuna': 'thon',
    'red tuna': 'thon rouge',
    'bluefin tuna': 'thon rouge',
    'albacore': 'thon',
    'yellowfin tuna': 'thon jaune',
    'bigeye tuna': 'thon',
    'skipjack': 'bonite',
    'skipjack tuna': 'bonite',
    'tuna fish': 'thon',

    // Dorade - Sea Bream
    'sea bream': 'dorade',
    'gilthead bream': 'dorade royale',
    'gilthead sea bream': 'dorade royale',
    'gilt head bream': 'sbares',
    'gilt-head bream': 'dorade royale',
    'royal sea bream': 'dorade royale',
    'dorado': 'dorade',
    'porgy': 'dorade',
    'daurade': 'dorade',

    // Sardine
    'sardine': 'serdina',
    'european sardine': 'serdina',
    'pilchard': 'serdina',
    'sardina': 'serdina',
    'small sardine': 'serdina',

    // Bar - Sea Bass
    'sea bass': 'bar',
    'european sea bass': 'bar',
    'bass': 'bar',
    'mediterranean sea bass': 'karous',
    'loup de mer': 'bar',
    'branzino': 'bar',

    // Maquereau - Mackerel
    'mackerel': 'maquereau',
    'atlantic mackerel': 'maquereau',
    'common mackerel': 'maquereau',
    'spanish mackerel': 'maquereau espagnol',

    // Merlu - Hake
    'hake': 'merlu',
    'european hake': 'merlu',
    'white hake': 'merlu',
    'silver hake': 'merlu',

    // Sole
    'sole': 'sole',
    'common sole': 'sole commune',
    'dover sole': 'sole',
    'flat fish': 'sole',
    'flatfish': 'sole',

    // Loup de mer - Sea Wolf
    'sea wolf': 'loup de mer',
    'wolf fish': 'loup de mer',
    'atlantic wolffish': 'loup de mer',

    // Rouget - Red Mullet
    'red mullet': 'rouget',
    'mullet': 'rouget',
    'surmullet': 'rouget',
    'striped red mullet': 'rouget',
    'goatfish': 'rouget',
    'rouget barbet': 'rouget',

    // Anchois - Anchovy
    'anchovy': 'anchois',
    'european anchovy': 'anchois',
    'anchovies': 'anchois',

    // Espadon - Swordfish
    'swordfish': 'espadon',
    'broadbill': 'espadon',
    'broadbill swordfish': 'espadon',

    // Autres espèces
    'squid': 'calamr',
    'octopus': 'karnit',
    'shrimp': 'crevettes',
    'prawn': 'chevrette',
    'scorpion fish': 'scorpaena',
    'grey mullet': 'bouri',
    'grouper': 'meeza',
    'red porgy': 'morjan',
    'common dentex': 'denté',
    'white seabream': 'sar',
    'ray': 'raie',
    'shark': 'requin',
    'conger eel': 'congre',
    'john dory': 'saint-pierre',
    'turbot': 'turbot',
    'monkfish': 'baudroie',
    'cod': 'cabillaud',
    'whiting': 'merlan',
    'gurnard': 'grondin',
    'red sea bream': 'pageot',
    'common seabream': 'pagre',
    'scorpionfish': 'rascasse',

    // Termes génériques
    'fish': 'poisson',
    'seafood': 'fruits de mer',
    'shellfish': 'crustacés',
    'marine': 'marin',
    'mediterranean fish': 'poisson méditerranéen',
  };

  /// Mapping des noms d'espèces en français vers les noms scientifiques
  static const Map<String, String> frenchToScientific = {
    'thon': 'Thunnus thynnus',
    'thon rouge': 'Thunnus thynnus',
    'dorade': 'Sparus aurata',
    'dorade royale': 'Sparus aurata',
    'serdina': 'Sardina pilchardus',
    'bar': 'Dicentrarchus labrax',
    'maquereau': 'Scomber scombrus',
    'merlu': 'Merluccius merluccius',
    'sole': 'Solea solea',
    'sole commune': 'Solea solea',
    'loup de mer': 'Anarhichas lupus',
    'rouget': 'Mullus surmuletus',
    'anchois': 'Engraulis encrasicolus',
    'espadon': 'Xiphias gladius',
    'calamr': 'Loligo vulgaris',
    'karnit': 'Octopus vulgaris',
    'crevettes': 'Penaeus spp.',
    'chevrette': 'Palaemon serratus',
    'scorpaena': 'Scorpaena scrofa',
    'sbares': 'Sparus aurata',
    'bouri': 'Mugil cephalus',
    'meeza': 'Epinephelus marginatus',
    'karous': 'Dicentrarchus labrax',
    'morjan': 'Pagrus pagrus',
    'denté': 'Dentex dentex',
    'sar': 'Diplodus sargus',
    'raie': 'Raja spp.',
    'requin': 'Carcharhinus spp.',
    'congre': 'Conger conger',
    'saint-pierre': 'Zeus faber',
    'turbot': 'Scophthalmus maximus',
    'baudroie': 'Lophius piscatorius',
    'bonite': 'Katsuwonus pelamis',
    'cabillaud': 'Gadus morhua',
    'merlan': 'Merlangius merlangus',
    'grondin': 'Trigla spp.',
    'pageot': 'Pagellus erythrinus',
    'pagre': 'Pagrus pagrus',
    'rascasse': 'Scorpaena porcus',
  };
}
