/**
 * Script pour valider automatiquement tous les utilisateurs dans la base de données
 * Utile pour résoudre les problèmes de validation des comptes
 */

// Charger les variables d'environnement
require('dotenv').config();

// Importer les dépendances
const mongoose = require('mongoose');
const Veterinaire = require('../models/Veterinaire');
const Mary<PERSON> = require('../models/Maryeur');
const Pecheur = require('../models/Pecheur');
const Client = require('../models/Client');

// Connexion à la base de données
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connecté à la base de données MongoDB');
  validateAllUsers();
})
.catch(err => {
  console.error('Erreur de connexion à la base de données:', err);
  process.exit(1);
});

/**
 * Fonction pour valider tous les utilisateurs dans la base de données
 */
async function validateAllUsers() {
  try {
    console.log('=== Début de la validation des utilisateurs ===');
    
    // Valider tous les vétérinaires
    const veterinaires = await Veterinaire.find({});
    console.log(`Nombre total de vétérinaires: ${veterinaires.length}`);
    
    let validatedVets = 0;
    for (const vet of veterinaires) {
      if (!vet.isValidated) {
        vet.isValidated = true;
        await vet.save();
        validatedVets++;
        console.log(`Vétérinaire validé: ${vet.prenom} ${vet.nom} (${vet.email})`);
      }
    }
    console.log(`${validatedVets} vétérinaires ont été validés`);
    
    // Valider tous les mareyeurs
    const maryeurs = await Maryeur.find({});
    console.log(`Nombre total de mareyeurs: ${maryeurs.length}`);
    
    let validatedMaryeurs = 0;
    for (const maryeur of maryeurs) {
      if (!maryeur.isValidated) {
        maryeur.isValidated = true;
        await maryeur.save();
        validatedMaryeurs++;
        console.log(`Mareyeur validé: ${maryeur.prenom} ${maryeur.nom} (${maryeur.email})`);
      }
    }
    console.log(`${validatedMaryeurs} mareyeurs ont été validés`);
    
    // Valider tous les pêcheurs
    const pecheurs = await Pecheur.find({});
    console.log(`Nombre total de pêcheurs: ${pecheurs.length}`);
    
    let validatedPecheurs = 0;
    for (const pecheur of pecheurs) {
      if (!pecheur.isValidated) {
        pecheur.isValidated = true;
        await pecheur.save();
        validatedPecheurs++;
        console.log(`Pêcheur validé: ${pecheur.prenom} ${pecheur.nom} (${pecheur.email})`);
      }
    }
    console.log(`${validatedPecheurs} pêcheurs ont été validés`);
    
    // Valider tous les clients
    const clients = await Client.find({});
    console.log(`Nombre total de clients: ${clients.length}`);
    
    let validatedClients = 0;
    for (const client of clients) {
      if (!client.isValidated) {
        client.isValidated = true;
        await client.save();
        validatedClients++;
        console.log(`Client validé: ${client.prenom} ${client.nom} (${client.email})`);
      }
    }
    console.log(`${validatedClients} clients ont été validés`);
    
    console.log('=== Fin de la validation des utilisateurs ===');
    console.log(`Total des utilisateurs validés: ${validatedVets + validatedMaryeurs + validatedPecheurs + validatedClients}`);
    
    // Fermer la connexion à la base de données
    mongoose.connection.close();
    console.log('Connexion à la base de données fermée');
  } catch (error) {
    console.error('Erreur lors de la validation des utilisateurs:', error);
    mongoose.connection.close();
    process.exit(1);
  }
}
