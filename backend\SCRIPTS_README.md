# 📋 **SCRIPTS BACKEND - SEATRACE**

## 🔧 **Scripts Disponibles**

### **1. 👨‍💼 Création des Administrateurs**

**Script :** `create-admin.js`  
**Description :** Crée les comptes administrateurs prédéfinis dans la base de données.

#### **🚀 Exécution :**

```bash
# Depuis le dossier backend/
npm run create-admin

# Ou directement avec Node.js
node src/scripts/create-admin.js
```

#### **📋 Comptes Administrateurs Créés :**

| **Email** | **Mot de passe** | **Nom** | **Prénom** |
|-----------|------------------|---------|------------|
| `<EMAIL>` | `Admin123*` | Admin | SeaTrace |
| `<EMAIL>` | `SafaAdmin1*` | Sfaxi | Safa |
| `<EMAIL>` | `NawresAdmin2*` | Othmen | Nawres |

#### **✅ Résultat Attendu :**
```
🔧 Création des comptes administrateurs...

✅ Administrateur créé avec succès: <EMAIL>
   Nom: SeaTrace Admin
   Téléphone: 12345678

✅ Administrateur créé avec succès: <EMAIL>
   Nom: Safa Sfaxi
   Téléphone: 29616410

✅ Administrateur créé avec succès: <EMAIL>
   Nom: Nawres Othmen
   Téléphone: 92413266

🎉 Création des administrateurs terminée avec succès !

📋 Comptes administrateurs disponibles :
   1. <EMAIL> - SeaTrace Admin ✅
   2. <EMAIL> - Safa Sfaxi ✅
   3. <EMAIL> - Nawres Othmen ✅

🔌 Connexion à MongoDB fermée
```

### **2. 🔄 Migration des Utilisateurs**

**Script :** `migrateUsers.js`  
**Description :** Migre et standardise les données utilisateurs existantes.

#### **🚀 Exécution :**

```bash
# Depuis le dossier backend/
npm run migrate-users

# Ou directement avec Node.js
node src/scripts/migrateUsers.js
```

## 📝 **Notes Importantes**

### **🔒 Sécurité :**
- Les mots de passe sont automatiquement chiffrés par le middleware du modèle
- Les comptes administrateurs sont créés avec `isValidated: true`
- Aucun compte n'est bloqué par défaut (`isBlocked: false`)

### **🗄️ Base de Données :**
- Les scripts utilisent la base de données `seatrace` (configurée dans `.env`)
- Connexion automatique à MongoDB avec gestion d'erreurs
- Fermeture propre de la connexion après exécution

### **⚠️ Prérequis :**
1. **MongoDB** doit être démarré
2. **Variables d'environnement** configurées dans `.env`
3. **Dépendances** installées (`npm install`)

### **🔍 Vérification :**
Après exécution, vous pouvez vérifier dans MongoDB Compass :
- **Collection :** `admins`
- **Base de données :** `seatrace`
- **Champs vérifiés :** `email`, `nom`, `prenom`, `isValidated`, `roles`

## 🛠️ **Dépannage**

### **❌ Erreur de Connexion MongoDB :**
```bash
Erreur de connexion à MongoDB: connect ECONNREFUSED 127.0.0.1:27017
```
**Solution :** Vérifier que MongoDB est démarré

### **❌ Administrateur Existe Déjà :**
```bash
⚠️  L'administrateur avec l'email <EMAIL> existe déjà.
```
**Solution :** Normal, le script évite les doublons automatiquement

### **❌ Erreur de Variables d'Environnement :**
**Solution :** Vérifier que le fichier `.env` existe et contient `MONGODB_URI`

## 🎯 **Utilisation Recommandée**

1. **Première installation :** Exécuter `create-admin` pour créer les comptes administrateurs
2. **Après modifications de données :** Exécuter `migrate-users` si nécessaire
3. **Tests :** Utiliser les comptes administrateurs créés pour tester l'interface admin

**🎣 SeaTrace - Scripts Backend Optimisés ! ✨**
