import 'package:flutter/material.dart';
import 'package:seatrace/utils/app_theme.dart';

/// Widget de carte amélioré pour afficher les lots et les enchères
class SeaLotCard extends StatelessWidget {
  /// Les données du lot à afficher
  final Map<String, dynamic> lot;

  /// L'action à effectuer lorsque l'utilisateur appuie sur la carte
  final VoidCallback? onTap;

  /// Les actions supplémentaires à afficher
  final List<Widget>? actions;

  /// Indique si le lot est en enchère
  final bool isAuction;

  /// Indique si le lot est vendu
  final bool isSold;

  /// Crée une nouvelle carte de lot.
  const SeaLotCard({
    super.key,
    required this.lot,
    this.onTap,
    this.actions,
    this.isAuction = false,
    this.isSold = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final imageUrl = lot['photo'] ?? '';
    final espece =
        lot['espece'] is String
            ? lot['espece']
            : lot['espece']?['nom'] ?? 'Poisson';
    final quantite = lot['quantite']?.toString() ?? '';
    final poids = lot['poids']?.toString() ?? '';
    final prix = lot['prixInitial'] ?? lot['prixMinimal'] ?? '';
    final status = _getStatus();

    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: _getBorderColor(theme), width: 1.5),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image et badge de statut
            Stack(
              children: [
                // Image
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  child:
                      imageUrl.isNotEmpty
                          ? Image.network(
                            imageUrl,
                            height: 150,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder:
                                (context, error, stackTrace) => Container(
                                  height: 150,
                                  color: theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  child: Icon(
                                    Icons.image_not_supported,
                                    size: 50,
                                    color: theme.colorScheme.primary.withValues(
                                      alpha: 0.5,
                                    ),
                                  ),
                                ),
                          )
                          : Container(
                            height: 150,
                            color: theme.colorScheme.primary.withValues(
                              alpha: 0.1,
                            ),
                            child: Icon(
                              Icons.image,
                              size: 50,
                              color: theme.colorScheme.primary.withValues(
                                alpha: 0.5,
                              ),
                            ),
                          ),
                ),
                // Badge de statut
                if (status != null)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(theme),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        status,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            // Contenu
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Titre et prix
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          espece,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (prix != '')
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '$prix TND',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Détails
                  if (quantite != '' || poids != '')
                    Row(
                      children: [
                        if (quantite != '') ...[
                          Icon(
                            Icons.numbers,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '$quantite unités',
                            style: theme.textTheme.bodyMedium,
                          ),
                          const SizedBox(width: 16),
                        ],
                        if (poids != '') ...[
                          Icon(
                            Icons.scale,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text('$poids kg', style: theme.textTheme.bodyMedium),
                        ],
                      ],
                    ),
                  // Actions
                  if (actions != null && actions!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: actions!,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Retourne le statut du lot
  String? _getStatus() {
    if (isSold) {
      return 'Vendu';
    } else if (isAuction) {
      return 'En enchère';
    } else if (lot['test'] == true && lot['status'] == true) {
      return 'Validé';
    } else if (lot['test'] == true && lot['status'] == false) {
      return 'Rejeté';
    } else if (lot['test'] == false) {
      return 'En attente';
    }
    return null;
  }

  /// Retourne la couleur du statut
  Color _getStatusColor(ThemeData theme) {
    if (isSold) {
      return AppTheme.successColor;
    } else if (isAuction) {
      return AppTheme.infoColor;
    } else if (lot['test'] == true && lot['status'] == true) {
      return AppTheme.successColor;
    } else if (lot['test'] == true && lot['status'] == false) {
      return AppTheme.errorColor;
    } else if (lot['test'] == false) {
      return AppTheme.warningColor;
    }
    return theme.colorScheme.primary;
  }

  /// Retourne la couleur de la bordure
  Color _getBorderColor(ThemeData theme) {
    if (isSold) {
      return AppTheme.successColor.withValues(alpha: 0.3);
    } else if (isAuction) {
      return AppTheme.infoColor.withValues(alpha: 0.3);
    } else if (lot['test'] == true && lot['status'] == true) {
      return AppTheme.successColor.withValues(alpha: 0.3);
    } else if (lot['test'] == true && lot['status'] == false) {
      return AppTheme.errorColor.withValues(alpha: 0.3);
    } else if (lot['test'] == false) {
      return AppTheme.warningColor.withValues(alpha: 0.3);
    }
    return theme.colorScheme.primary.withValues(alpha: 0.3);
  }
}
