// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'SeaTrace';

  @override
  String get loginTitle => 'Connexion';

  @override
  String get email => 'Email';

  @override
  String get password => 'Mot de passe';

  @override
  String get login => 'Se connecter';

  @override
  String get register => 'S\'inscrire';

  @override
  String get forgotPassword => 'Mot de passe oublié ?';

  @override
  String get serverIp => 'Adresse IP du serveur';

  @override
  String get testConnection => 'Tester la connexion';

  @override
  String get applyIp => 'Appliquer';

  @override
  String get discoverServers => 'Découvrir les serveurs';

  @override
  String welcomeBack(Object name) {
    return 'Bienvenue, $name';
  }

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get profile => 'Profil';

  @override
  String get settings => 'Paramètres';

  @override
  String get logout => 'Déconnexion';

  @override
  String get notifications => 'Notifications';

  @override
  String get noNotifications => 'Aucune notification';

  @override
  String get markAllAsRead => 'Marquer tout comme lu';

  @override
  String get language => 'Langue';

  @override
  String get french => 'Français';

  @override
  String get arabic => 'العربية';

  @override
  String get chooseLanguage => 'Choisir la langue';

  @override
  String get save => 'Enregistrer';

  @override
  String get cancel => 'Annuler';

  @override
  String get error => 'Erreur';

  @override
  String get success => 'Succès';

  @override
  String get warning => 'Avertissement';

  @override
  String get info => 'Information';

  @override
  String get loading => 'Chargement...';

  @override
  String get retry => 'Réessayer';

  @override
  String get noData => 'Aucune donnée disponible';

  @override
  String get scanFish => 'Scanner un poisson';

  @override
  String get fishDetails => 'Détails du poisson';

  @override
  String get weight => 'Poids';

  @override
  String get temperature => 'Température';

  @override
  String get fishingMethod => 'Méthode de pêche';

  @override
  String get fishingZone => 'Zone de pêche';

  @override
  String get location => 'Emplacement';

  @override
  String get selectMareyeur => 'Sélectionner un mareyeur';

  @override
  String get selectVeterinarian => 'Sélectionner un vétérinaire';

  @override
  String get submit => 'Soumettre';

  @override
  String get pendingLots => 'Lots en attente';

  @override
  String get approvedLots => 'Lots approuvés';

  @override
  String get rejectedLots => 'Lots refusés';

  @override
  String get viewPendingLots => 'Voir les lots en attente';

  @override
  String get statistics => 'Statistiques';

  @override
  String get recentActivity => 'Activité récente';

  @override
  String get viewAll => 'Voir tout';

  @override
  String get noRecentActivity => 'Aucune activité récente';

  @override
  String get approve => 'Approuver';

  @override
  String get reject => 'Rejeter';

  @override
  String get rejectionReason => 'Motif de rejet';

  @override
  String get initialPrice => 'Prix initial';

  @override
  String get minimalPrice => 'Prix minimal';

  @override
  String get startAuction => 'Démarrer l\'enchère';

  @override
  String get activeAuctions => 'Enchères actives';

  @override
  String get completedAuctions => 'Enchères terminées';

  @override
  String get availableAuctions => 'Enchères disponibles';

  @override
  String get myPurchases => 'Mes achats';

  @override
  String get bid => 'Enchérir';

  @override
  String get currentBid => 'Enchère actuelle';

  @override
  String get timeRemaining => 'Temps restant';

  @override
  String get extendAuction => 'Prolonger l\'enchère';

  @override
  String get closeAuction => 'Clôturer l\'enchère';

  @override
  String get auctionClosed => 'Enchère clôturée';

  @override
  String get youWon => 'Vous avez remporté cette enchère !';

  @override
  String get confirmSpecies => 'Confirmer cette espèce';

  @override
  String get refreshCounters => 'Rafraîchir les compteurs';

  @override
  String get countersRefreshed => 'Compteurs rafraîchis avec succès';

  @override
  String get changeLanguage => 'Changer de langue';

  @override
  String get languageChanged => 'Langue changée avec succès';
}
