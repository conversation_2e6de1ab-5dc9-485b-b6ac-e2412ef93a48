name: seatrace
description: "Application de traçabilité et de vente de produits de la mer"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  provider: ^6.0.5
  shared_preferences: ^2.5.3
  http: ^1.1.0
  http_parser: ^4.0.2
  path: ^1.8.3
  path_provider: ^2.1.1
  uuid: ^4.0.0
  image_picker: ^1.0.4
  intl: ^0.19.0
  image: ^4.5.4
  tflite_flutter: ^0.11.0
  geolocator: ^14.0.0
  dio: ^5.4.1
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  video_player: ^2.9.5
  get: ^4.7.2
  connectivity_plus: ^6.1.4
  network_info_plus: ^5.0.2
  # Nouvelles dépendances pour le design
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  flutter_cache_manager: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^3.1.0
  flutter_animate: ^4.5.0
  # WebSocket pour les notifications en temps réel
  web_socket_channel: ^2.4.0
  # Graphiques pour les statistiques
  fl_chart: ^0.66.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Configuration pour l'internationalisation
  generate: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - lib/assets/vid/fish_on.mp4
    - assets/models/fish_recognition_model.tflite
    - assets/models/keras_model.h5
    - assets/models/labels.txt

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font.
  #
  # Nous utilisons google_fonts au lieu de polices personnalisées
  # fonts:
  #   - family: Montserrat
  #     fonts:
  #       - asset: assets/fonts/Montserrat-Regular.ttf
  #       - asset: assets/fonts/Montserrat-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Montserrat-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Montserrat-Bold.ttf
  #         weight: 700
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
