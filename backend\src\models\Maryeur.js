const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');

const maryeurSchema = new mongoose.Schema({
  // Informations d'identification
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: [validator.isEmail, 'Email invalide']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  roles: {
    type: String,
    required: true,
    default: 'ROLE_MARYEUR'
  },

  // Informations personnelles
  nom: {
    type: String,
    required: true,
    trim: true
  },
  prenom: {
    type: String,
    required: true,
    trim: true
  },
  telephone: {
    type: String,
    required: true
  },
  photo: String,

  // Informations professionnelles
  cin: {
    type: String,
    required: true,
    unique: true
  },
  matricule: {
    type: String,
    required: true,
    unique: true
  },
  port: String,
  adresse: String,
  societe: String,

  // Statut du compte
  isValidated: {
    type: Boolean,
    default: false
  },
  isBlocked: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(_, ret) {
        ret.id =  ret._id.toString(); 
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

/**
 * Middleware: Hash du mot de passe avant sauvegarde
 */
maryeurSchema.pre('save', async function(next) {
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 8);
  }
  next();
});

/**
 * Méthode: Vérification du mot de passe
 */
maryeurSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

/**
 * Méthode: Vérification de rôle
 */
maryeurSchema.methods.hasRole = function(role) {
  return this.roles.includes(role);
};

/**
 * Méthode: Obtention du type d'utilisateur
 */
maryeurSchema.methods.getUserType = function() {
  return 'maryeur';
};

const Maryeur = mongoose.model('Maryeur', maryeurSchema);

module.exports = Maryeur;