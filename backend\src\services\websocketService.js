/**
 * Service WebSocket pour les enchères et notifications en temps réel
 * Gère les connexions WebSocket et la diffusion des mises à jour d'enchères
 */
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { BadRequestError, UnauthorizedError } = require('../middleware/errorHandler');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Map pour stocker les clients connectés par userId
    this.auctionRooms = new Map(); // Map<lotId, Set<userId>> pour les salles d'enchères
  }

  /**
   * Initialise le serveur WebSocket
   * @param {Object} server - Serveur HTTP
   */
  initialize(server) {
    // Créer un serveur WebSocket
    this.wss = new WebSocket.Server({ server });

    // Gérer les connexions
    this.wss.on('connection', (ws, req) => {
      // Extraire le token de l'URL
      const url = new URL(req.url, `http://${req.headers.host}`);
      const token = url.searchParams.get('token');

      if (!token) {
        ws.close(4001, 'Token manquant');
        return;
      }

      try {
        // Vérifier le token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const userId = decoded.id || decoded._id;

        if (!userId) {
          ws.close(4002, 'ID utilisateur manquant dans le token');
          return;
        }

        // Stocker le client avec son ID utilisateur
        if (!this.clients.has(userId)) {
          this.clients.set(userId, []);
        }
        this.clients.get(userId).push(ws);

        console.log(`[${new Date().toISOString()}] WebSocket: Nouvelle connexion pour l'utilisateur ${userId}`);

        // Envoyer un message de confirmation
        ws.send(JSON.stringify({
          type: 'connection',
          message: 'Connecté au serveur WebSocket',
          userId
        }));

        // Gérer les messages
        ws.on('message', (message) => {
          try {
            const data = JSON.parse(message);
            console.log(`[${new Date().toISOString()}] WebSocket: Message reçu de l'utilisateur ${userId}:`, data);
          } catch (error) {
            console.error(`[${new Date().toISOString()}] WebSocket: Erreur lors du traitement du message:`, error);
          }
        });

        // Gérer les déconnexions
        ws.on('close', () => {
          console.log(`[${new Date().toISOString()}] WebSocket: Déconnexion de l'utilisateur ${userId}`);

          // Supprimer le client de la liste
          if (this.clients.has(userId)) {
            const clients = this.clients.get(userId);
            const index = clients.indexOf(ws);
            if (index !== -1) {
              clients.splice(index, 1);
            }

            // Si plus aucun client pour cet utilisateur, supprimer l'entrée
            if (clients.length === 0) {
              this.clients.delete(userId);
            }
          }
        });

        // Gérer les erreurs
        ws.on('error', (error) => {
          console.error(`[${new Date().toISOString()}] WebSocket: Erreur pour l'utilisateur ${userId}:`, error);
        });
      } catch (error) {
        console.error(`[${new Date().toISOString()}] WebSocket: Erreur d'authentification:`, error);
        ws.close(4003, 'Token invalide');
      }
    });

    console.log(`[${new Date().toISOString()}] WebSocket: Serveur initialisé`);
  }

  /**
   * Envoie une notification à un utilisateur spécifique
   * @param {string} userId - ID de l'utilisateur
   * @param {Object} notification - Notification à envoyer
   */
  sendNotification(userId, notification) {
    if (!this.wss) {
      console.error(`[${new Date().toISOString()}] WebSocket: Serveur non initialisé`);
      return;
    }

    if (!this.clients.has(userId)) {
      // L'utilisateur n'est pas connecté
      return;
    }

    const clients = this.clients.get(userId);
    const message = JSON.stringify({
      type: 'notification',
      data: notification
    });

    // Envoyer la notification à tous les clients de cet utilisateur
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });

    console.log(`[${new Date().toISOString()}] WebSocket: Notification envoyée à l'utilisateur ${userId}`);
  }

  /**
   * Envoie une notification à tous les utilisateurs d'un type spécifique
   * @param {string} userType - Type d'utilisateur (Pecheur, Veterinaire, Maryeur, Client, Admin)
   * @param {Object} notification - Notification à envoyer
   */
  sendNotificationByUserType(userType, notification) {
    // Cette méthode nécessite d'accéder à la base de données pour récupérer les IDs des utilisateurs
    // du type spécifié, puis d'appeler sendNotification pour chaque ID
    const mongoose = require('mongoose');
    let Model;

    switch (userType) {
      case 'Pecheur':
        Model = require('../models/Pecheur');
        break;
      case 'Veterinaire':
        Model = require('../models/Veterinaire');
        break;
      case 'Maryeur':
        Model = require('../models/Maryeur');
        break;
      case 'Client':
        Model = require('../models/Client');
        break;
      case 'Admin':
        Model = require('../models/Admin');
        break;
      default:
        console.error(`[${new Date().toISOString()}] WebSocket: Type d'utilisateur non pris en charge: ${userType}`);
        return;
    }

    Model.find({}, '_id').then(users => {
      users.forEach(user => {
        this.sendNotification(user._id.toString(), notification);
      });
    }).catch(error => {
      console.error(`[${new Date().toISOString()}] WebSocket: Erreur lors de la récupération des utilisateurs:`, error);
    });
  }

  /**
   * Envoie un message à tous les clients connectés
   * @param {string} type - Type de message
   * @param {Object} data - Données à envoyer
   */
  broadcastToAll(type, data) {
    if (!this.wss) {
      console.error(`[${new Date().toISOString()}] WebSocket: Serveur non initialisé`);
      return;
    }

    const message = JSON.stringify({
      type,
      data
    });

    // Parcourir tous les clients connectés
    this.clients.forEach((clients, userId) => {
      clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
        }
      });
    });

    console.log(`[${new Date().toISOString()}] WebSocket: Message de type "${type}" diffusé à tous les clients connectés`);
  }

  /**
   * Fait rejoindre un utilisateur à une salle d'enchère
   */
  joinAuctionRoom(userId, lotId) {
    if (!this.auctionRooms.has(lotId)) {
      this.auctionRooms.set(lotId, new Set());
    }

    this.auctionRooms.get(lotId).add(userId);

    console.log(`🏠 ${userId} a rejoint l'enchère ${lotId}`);

    // Confirmer l'adhésion
    this.sendToUser(userId, {
      type: 'auction_joined',
      data: { lotId }
    });
  }

  /**
   * Fait quitter un utilisateur d'une salle d'enchère
   */
  leaveAuctionRoom(userId, lotId) {
    if (this.auctionRooms.has(lotId)) {
      this.auctionRooms.get(lotId).delete(userId);

      if (this.auctionRooms.get(lotId).size === 0) {
        this.auctionRooms.delete(lotId);
      }
    }

    console.log(`🚪 ${userId} a quitté l'enchère ${lotId}`);
  }

  /**
   * Envoie un message à un utilisateur spécifique
   */
  sendToUser(userId, message) {
    if (!this.clients.has(userId)) return false;

    const clients = this.clients.get(userId);
    let sent = false;

    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
        sent = true;
      }
    });

    return sent;
  }

  /**
   * Diffuse une mise à jour d'enchère à tous les participants
   */
  broadcastAuctionUpdate(lotId, updateData) {
    const participants = this.auctionRooms.get(lotId);
    if (!participants) return;

    const message = {
      type: 'auction_update',
      data: {
        lotId,
        ...updateData,
        timestamp: new Date().toISOString()
      }
    };

    let sentCount = 0;
    for (const userId of participants) {
      if (this.sendToUser(userId, message)) {
        sentCount++;
      }
    }

    console.log(`📡 Diffusion enchère ${lotId}: ${sentCount}/${participants.size} participants`);
  }

  /**
   * Diffuse le début d'une nouvelle enchère
   */
  broadcastAuctionStart(lotData) {
    const message = {
      type: 'auction_started',
      data: {
        lotId: lotData._id,
        identifiant: lotData.identifiant,
        especeNom: lotData.especeNom,
        poids: lotData.poids,
        prixInitial: lotData.prixInitial,
        prixMinimal: lotData.prixMinimal,
        dateFinEnchere: lotData.dateFinEnchere,
        timestamp: new Date().toISOString()
      }
    };

    this.broadcastToAll('auction_started', message.data);
    console.log(`🚀 Nouvelle enchère diffusée: ${lotData.identifiant}`);
  }

  /**
   * Diffuse la fin d'une enchère
   */
  broadcastAuctionEnd(lotId, finalData) {
    const participants = this.auctionRooms.get(lotId);
    if (!participants) return;

    const message = {
      type: 'auction_ended',
      data: {
        lotId,
        ...finalData,
        timestamp: new Date().toISOString()
      }
    };

    for (const userId of participants) {
      this.sendToUser(userId, message);
    }

    // Nettoyer la salle d'enchère
    this.auctionRooms.delete(lotId);

    console.log(`🏁 Fin d'enchère diffusée: ${lotId}`);
  }

  /**
   * Diffuse une nouvelle enchère (bid)
   */
  broadcastNewBid(lotId, bidData) {
    this.broadcastAuctionUpdate(lotId, {
      type: 'new_bid',
      prixEnchere: bidData.amount,
      acheteur: bidData.bidder,
      timeRemaining: bidData.timeRemaining
    });
  }

  /**
   * Diffuse une extension de temps d'enchère
   */
  broadcastTimeExtension(lotId, newEndTime) {
    this.broadcastAuctionUpdate(lotId, {
      type: 'time_extended',
      dateFinEnchere: newEndTime,
      message: 'Temps d\'enchère prolongé'
    });
  }

  /**
   * Obtient le nombre de participants à une enchère
   */
  getAuctionParticipants(lotId) {
    const participants = this.auctionRooms.get(lotId);
    return participants ? participants.size : 0;
  }

  /**
   * Obtient les statistiques des connexions
   */
  getStats() {
    return {
      totalConnections: this.clients.size,
      activeAuctions: this.auctionRooms.size,
      totalParticipants: Array.from(this.auctionRooms.values())
        .reduce((total, participants) => total + participants.size, 0)
    };
  }
}

// Exporter une instance unique du service
module.exports = new WebSocketService();
