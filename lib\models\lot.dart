import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/services/model_service.dart';

/// Modèle standardisé pour les lots de poissons
/// Version simplifiée sans référence à Espece ou Prise
class Lot {
  final String id;
  final String identifiant;
  final String? photo; // Stocke le chemin ou l'URL de l'image
  final int quantite;
  final double poids;
  final String
  especeNom; // Nom de l'espèce directement (sans référence à Espece)
  final double temperature;
  final DateTime? dateTest;
  final bool test;
  final bool isValidated;
  // Getter pour la compatibilité avec le code existant
  bool get isValid => isValidated;
  final bool vendu;
  final String userId;
  final DateTime? dateSoumission;
  final double? prixInitial;
  final double? prixMinimal;
  final double? prixFinal;
  // Getters pour la compatibilité avec le code existant
  double? get prixDepart => prixInitial;
  double? get prixFinale => prixFinal;
  final String? acheteurId;
  final String? motifRefus;
  final String? raisonRejet; // Alias pour motifRefus
  final String? lieu;

  // Getter pour compatibilité avec les anciens écrans
  String? get acheteur => acheteurId;
  final String? pecheurId;
  final String? veterinaireId;
  final String? maryeurId;
  final Pecheur? pecheur;
  final Veterinaire? veterinaire;
  final Maryeur? maryeur;
  final double? prixActuel;
  final String? description;

  // Champs manquants du backend
  final DateTime? dateValidation;
  final DateTime? dateEnchere;
  final double? prixEnchere;

  // Pour la compatibilité avec le code existant
  String get especeId =>
      ''; // Champ vide car nous n'utilisons plus de référence à Espece

  // Getter pour la compatibilité avec le code existant
  // Ce getter est maintenu pour la compatibilité avec le code existant
  // mais nous utiliserons directement 'photo' dans tout le code nouveau
  String? get imageUrl => photo;

  // Champs standardisés pour les enchères
  final bool? isAuctionActive;
  // Getter pour la compatibilité avec le code existant
  bool? get enchereActive => isAuctionActive;
  final DateTime? dateFinEnchere;
  final DateTime? dateDebutEnchere;
  final bool? online;
  final String? typeEnchere;
  Lot({
    required this.id,
    required this.identifiant,
    this.photo,
    required this.quantite,
    required this.poids,
    required this.especeNom,
    required this.temperature,
    this.dateTest,
    required this.test,
    required this.isValidated,
    required this.vendu,
    required this.userId,
    required this.dateSoumission,
    this.prixInitial,
    this.prixMinimal,
    this.prixFinal,
    this.acheteurId,
    this.motifRefus,
    this.raisonRejet,
    this.lieu,
    this.pecheurId,
    this.veterinaireId,
    this.maryeurId,
    this.pecheur,
    this.veterinaire,
    this.maryeur,
    this.prixActuel,
    this.description,
    this.dateValidation,
    this.dateEnchere,
    this.prixEnchere,
    this.isAuctionActive,
    // enchereActive est maintenant un getter basé sur isAuctionActive
    this.dateFinEnchere,
    this.dateDebutEnchere,
    this.online,
    this.typeEnchere,
  });

  /// Convertit une Map en objet Lot
  ///
  /// Cette méthode prend une Map (généralement issue d'une réponse JSON)
  /// et la convertit en objet Lot. Elle gère les différentes conventions
  /// de nommage et les valeurs manquantes.
  ///
  /// @param map La Map à convertir
  /// @return Un objet Lot
  factory Lot.fromMap(Map<String, dynamic> map) {
    // Vérifier que la map n'est pas vide
    if (map.isEmpty) {
      throw ArgumentError('La map ne peut pas être vide');
    }

    // Créer une instance de ModelService pour standardiser les clés
    final modelService = ModelService();
    final standardMap = modelService.standardizeMap(map);

    // Extraire les objets imbriqués si présents
    Pecheur? pecheur;
    if (standardMap.containsKey('pecheur') && standardMap['pecheur'] != null) {
      pecheur = Pecheur.fromMap(standardMap['pecheur']);
    }

    Veterinaire? veterinaire;
    if (standardMap.containsKey('veterinaire') &&
        standardMap['veterinaire'] != null) {
      veterinaire = Veterinaire.fromMap(standardMap['veterinaire']);
    }

    Maryeur? maryeur;
    if (standardMap.containsKey('maryeur') && standardMap['maryeur'] != null) {
      maryeur = Maryeur.fromMap(standardMap['maryeur']);
    }

    return Lot(
      id: standardMap['_id']?.toString() ?? standardMap['id']?.toString() ?? '',
      identifiant: standardMap['identifiant'] ?? '',
      photo: standardMap['photo'] ?? standardMap['imageUrl'],
      quantite: _parseInt(standardMap['quantite']) ?? 0,
      poids: _parseDouble(standardMap['poids'] ?? standardMap['poid']) ?? 0.0,
      especeNom:
          standardMap['especeNom'] ??
          standardMap['espece_nom'] ??
          standardMap['espece']?.toString() ??
          'Poisson',
      temperature: _parseDouble(standardMap['temperature']) ?? 0.0,
      dateTest: _parseDateTime(
        standardMap['dateTest'] ?? standardMap['datetest'],
      ),
      test: _parseBool(standardMap['test']),
      isValidated: _parseBool(
        standardMap['status'] ??
            standardMap['isValid'] ??
            standardMap['is_valid'] ??
            standardMap['isValidated'],
      ),
      vendu: _parseBool(standardMap['vendu'] ?? standardMap['vendre']),
      userId:
          standardMap['user']?.toString() ??
          standardMap['userId']?.toString() ??
          standardMap['user_id']?.toString() ??
          '',
      dateSoumission: _parseDateTime(
        standardMap['dateSoumission'] ?? standardMap['datesoumettre'],
      ),
      prixInitial: _parseDouble(
        standardMap['prixInitial'] ?? standardMap['prixinitial'],
      ),
      prixMinimal: _parseDouble(
        standardMap['prixMinimal'] ?? standardMap['prixminimal'],
      ),
      prixFinal: _parseDouble(
        standardMap['prixFinal'] ?? standardMap['prixfinale'],
      ),
      acheteurId:
          standardMap['acheteur']?.toString() ??
          standardMap['acheteurId']?.toString(),
      motifRefus: standardMap['motifRefus'],
      raisonRejet: standardMap['raisonRejet'] ?? standardMap['motifRefus'],
      lieu: standardMap['lieu'],
      pecheurId: standardMap['pecheurId'] ?? standardMap['userId'],
      veterinaireId: standardMap['veterinaireId'],
      maryeurId: standardMap['maryeurId'] ?? standardMap['maryeur']?.toString(),
      pecheur: pecheur,
      veterinaire: veterinaire,
      maryeur: maryeur,
      prixActuel: _parseDouble(
        standardMap['prixActuel'] ?? standardMap['prixFinal'],
      ),
      description: standardMap['description'],
      dateValidation: _parseDateTime(standardMap['dateValidation']),
      dateEnchere: _parseDateTime(standardMap['dateEnchere']),
      prixEnchere: _parseDouble(standardMap['prixEnchere']),
      // Utiliser isAuctionActive ou enchereActive, en privilégiant isAuctionActive
      isAuctionActive: _parseBool(
        standardMap['isAuctionActive'] ?? standardMap['enchereActive'],
      ),
      dateFinEnchere:
          standardMap['dateFinEnchere'] != null
              ? DateTime.tryParse(standardMap['dateFinEnchere'])
              : null,
      dateDebutEnchere:
          standardMap['dateDebutEnchere'] != null
              ? DateTime.tryParse(standardMap['dateDebutEnchere'])
              : null,
      online: _parseBool(standardMap['online']),
      typeEnchere: standardMap['typeEnchere'] ?? 'standard',
    );
  }

  Map<String, dynamic> toMap() {
    final Map<String, dynamic> map = {
      'id': id,
      'identifiant': identifiant,
      'photo': photo,
      'quantite': quantite,
      'poids': poids,
      'especeNom': especeNom,
      'temperature': temperature,
      'dateTest': dateTest?.toIso8601String(),
      'test': test,
      'isValidated': isValidated, // Utiliser isValidated de manière cohérente
      'vendu': vendu,
      'user': userId,
      'dateSoumission': dateSoumission?.toIso8601String(),
      'prixInitial': prixInitial,
      'prixMinimal': prixMinimal,
      'prixFinal': prixFinal,
      'acheteur': acheteurId,
      'motifRefus': motifRefus,
      'raisonRejet': raisonRejet,
      'lieu': lieu,
    };

    // Ajouter les champs supplémentaires s'ils ne sont pas null
    if (pecheurId != null) map['pecheurId'] = pecheurId;
    if (veterinaireId != null) map['veterinaireId'] = veterinaireId;
    if (maryeurId != null) map['maryeurId'] = maryeurId;
    if (pecheur != null) map['pecheur'] = pecheur!.toMap();
    if (veterinaire != null) map['veterinaire'] = veterinaire!.toMap();
    if (maryeur != null) map['maryeur'] = maryeur!.toMap();
    if (prixActuel != null) map['prixActuel'] = prixActuel;
    if (description != null) map['description'] = description;
    if (dateValidation != null) {
      map['dateValidation'] = dateValidation!.toIso8601String();
    }
    if (dateEnchere != null) {
      map['dateEnchere'] = dateEnchere!.toIso8601String();
    }
    if (prixEnchere != null) map['prixEnchere'] = prixEnchere;
    // Utiliser isAuctionActive comme champ standardisé
    if (isAuctionActive != null) {
      map['isAuctionActive'] = isAuctionActive;
    }
    if (dateFinEnchere != null) {
      map['dateFinEnchere'] = dateFinEnchere!.toIso8601String();
    }
    if (dateDebutEnchere != null) {
      map['dateDebutEnchere'] = dateDebutEnchere!.toIso8601String();
    }
    if (online != null) {
      map['online'] = online;
    }
    if (typeEnchere != null) {
      map['typeEnchere'] = typeEnchere;
    }

    return map;
  }

  /// Créer une copie du lot avec des modifications
  Lot copyWith({
    String? id,
    String? identifiant,
    String? photo,
    int? quantite,
    double? poids,
    String? especeNom,
    double? temperature,
    DateTime? dateTest,
    bool? test,
    bool? isValidated,
    bool? vendu,
    String? userId,
    DateTime? dateSoumission,
    double? prixInitial,
    double? prixMinimal,
    double? prixFinal,
    String? acheteurId,
    String? motifRefus,
    String? raisonRejet,
    String? lieu,
    String? pecheurId,
    String? veterinaireId,
    String? maryeurId,
    Pecheur? pecheur,
    Veterinaire? veterinaire,
    Maryeur? maryeur,
    double? prixActuel,
    String? description,
    DateTime? dateValidation,
    DateTime? dateEnchere,
    double? prixEnchere,
    bool? isAuctionActive,
    DateTime? dateFinEnchere,
    DateTime? dateDebutEnchere,
    bool? online,
    String? typeEnchere,
  }) {
    return Lot(
      id: id ?? this.id,
      identifiant: identifiant ?? this.identifiant,
      photo: photo ?? this.photo,
      quantite: quantite ?? this.quantite,
      poids: poids ?? this.poids,
      especeNom: especeNom ?? this.especeNom,
      temperature: temperature ?? this.temperature,
      dateTest: dateTest ?? this.dateTest,
      test: test ?? this.test,
      isValidated: isValidated ?? this.isValidated,
      vendu: vendu ?? this.vendu,
      userId: userId ?? this.userId,
      dateSoumission: dateSoumission ?? this.dateSoumission,
      prixInitial: prixInitial ?? this.prixInitial,
      prixMinimal: prixMinimal ?? this.prixMinimal,
      prixFinal: prixFinal ?? this.prixFinal,
      acheteurId: acheteurId ?? this.acheteurId,
      motifRefus: motifRefus ?? this.motifRefus,
      raisonRejet: raisonRejet ?? this.raisonRejet,
      lieu: lieu ?? this.lieu,
      pecheurId: pecheurId ?? this.pecheurId,
      veterinaireId: veterinaireId ?? this.veterinaireId,
      maryeurId: maryeurId ?? this.maryeurId,
      pecheur: pecheur ?? this.pecheur,
      veterinaire: veterinaire ?? this.veterinaire,
      maryeur: maryeur ?? this.maryeur,
      prixActuel: prixActuel ?? this.prixActuel,
      description: description ?? this.description,
      dateValidation: dateValidation ?? this.dateValidation,
      dateEnchere: dateEnchere ?? this.dateEnchere,
      prixEnchere: prixEnchere ?? this.prixEnchere,
      isAuctionActive: isAuctionActive ?? this.isAuctionActive,
      dateFinEnchere: dateFinEnchere ?? this.dateFinEnchere,
      dateDebutEnchere: dateDebutEnchere ?? this.dateDebutEnchere,
      online: online ?? this.online,
      typeEnchere: typeEnchere ?? this.typeEnchere,
    );
  }

  /// Convertit une valeur en double
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (_) {
        return null;
      }
    }
    return null;
  }

  /// Convertit une valeur en int
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (_) {
        return null;
      }
    }
    return null;
  }

  /// Convertit une valeur en bool
  static bool _parseBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    return false;
  }

  /// Convertit une valeur en DateTime
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (_) {
        // Si le format standard échoue, essayer d'autres formats
        try {
          // Format DD/MM/YYYY
          final parts = value.split('/');
          if (parts.length == 3) {
            return DateTime(
              int.parse(parts[2]), // année
              int.parse(parts[1]), // mois
              int.parse(parts[0]), // jour
            );
          }
        } catch (_) {}
        return null;
      }
    }
    return null;
  }
}
