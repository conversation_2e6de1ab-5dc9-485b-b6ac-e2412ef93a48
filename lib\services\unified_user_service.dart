import 'dart:io';

import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/client.dart';
import 'package:seatrace/models/admin.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/utils/error_handler.dart';

/// Service unifié pour la gestion des utilisateurs
class UnifiedUserService {
  static final UnifiedUserService _instance = UnifiedUserService._internal();
  factory UnifiedUserService() => _instance;
  UnifiedUserService._internal();

  final UnifiedApiService _apiService = UnifiedApiService();
  final UnifiedAuthService _authService = UnifiedAuthService();

  /// Récupère tous les pêcheurs
  Future<List<Pecheur>> getAllPecheurs() async {
    try {
      final response = await _apiService.get('pecheurs');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> data = response['data'];
        return data.map((item) => Pecheur.fromMap(item)).toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.getAllPecheurs',
      );
      return [];
    }
  }

  /// Récupère tous les vétérinaires
  /// [includeAll] Si true, récupère tous les vétérinaires, y compris ceux qui ne sont pas validés
  Future<List<Veterinaire>> getAllVeterinaires({
    bool includeAll = false,
  }) async {
    try {
      // Toujours récupérer tous les vétérinaires validés
      // Nous utilisons toujours l'endpoint de base qui retourne les vétérinaires validés
      final endpoint = 'veterinaires';

      // Journaliser la requête pour le débogage
      ErrorHandler.instance.logInfo(
        'Récupération des vétérinaires avec endpoint: $endpoint',
        context: 'UnifiedUserService.getAllVeterinaires',
      );

      final response = await _apiService.get(endpoint);

      // Journaliser la réponse pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse de l\'API pour les vétérinaires: ${response.containsKey('data') ? 'Contient data avec ${response['data'] is List ? (response['data'] as List).length : 'non-liste'}' : 'Pas de data'}',
        context: 'UnifiedUserService.getAllVeterinaires',
      );

      // Vérifier si les données sont dans data.data (structure imbriquée)
      if (response.containsKey('data') &&
          response['data'] is Map &&
          response['data'].containsKey('data') &&
          response['data']['data'] is List) {
        final List<dynamic> data = response['data']['data'];

        // Journaliser le nombre de vétérinaires récupérés
        ErrorHandler.instance.logInfo(
          'Nombre de vétérinaires récupérés (structure imbriquée): ${data.length}',
          context: 'UnifiedUserService.getAllVeterinaires',
        );
      } else if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> data = response['data'];

        // Journaliser le nombre de vétérinaires récupérés
        ErrorHandler.instance.logInfo(
          'Nombre de vétérinaires récupérés: ${data.length}',
          context: 'UnifiedUserService.getAllVeterinaires',
        );

        // Journaliser les détails des vétérinaires pour le débogage
        if (data.isNotEmpty) {
          ErrorHandler.instance.logInfo(
            'Premier vétérinaire: ${data.first}',
            context: 'UnifiedUserService.getAllVeterinaires',
          );
        }

        return data.map((item) => Veterinaire.fromMap(item)).toList();
      } else if (response.containsKey('data') &&
          response['data'] is Map &&
          response['data'].containsKey('data') &&
          response['data']['data'] is List) {
        final List<dynamic> data = response['data']['data'];

        // Journaliser les détails des vétérinaires pour le débogage
        if (data.isNotEmpty) {
          ErrorHandler.instance.logInfo(
            'Premier vétérinaire (structure imbriquée): ${data.first}',
            context: 'UnifiedUserService.getAllVeterinaires',
          );
        }

        return data.map((item) => Veterinaire.fromMap(item)).toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.getAllVeterinaires',
      );
      return [];
    }
  }

  /// Récupère tous les maryeurs
  /// [includeAll] Si true, récupère tous les maryeurs, y compris ceux qui ne sont pas validés
  Future<List<Maryeur>> getAllMaryeurs({bool includeAll = false}) async {
    try {
      // Toujours récupérer tous les maryeurs validés
      // Nous utilisons toujours l'endpoint de base qui retourne les maryeurs validés
      final endpoint = 'maryeurs';

      // Journaliser la requête pour le débogage
      ErrorHandler.instance.logInfo(
        'Récupération des maryeurs avec endpoint: $endpoint',
        context: 'UnifiedUserService.getAllMaryeurs',
      );

      final response = await _apiService.get(endpoint);

      // Journaliser la réponse pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse de l\'API pour les maryeurs: ${response.containsKey('data') ? 'Contient data avec ${response['data'] is List ? (response['data'] as List).length : 'non-liste'}' : 'Pas de data'}',
        context: 'UnifiedUserService.getAllMaryeurs',
      );

      // Vérifier si les données sont dans data.data (structure imbriquée)
      if (response.containsKey('data') &&
          response['data'] is Map &&
          response['data'].containsKey('data') &&
          response['data']['data'] is List) {
        final List<dynamic> data = response['data']['data'];

        // Journaliser le nombre de maryeurs récupérés
        ErrorHandler.instance.logInfo(
          'Nombre de maryeurs récupérés (structure imbriquée): ${data.length}',
          context: 'UnifiedUserService.getAllMaryeurs',
        );
      } else if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> data = response['data'];

        // Journaliser le nombre de maryeurs récupérés
        ErrorHandler.instance.logInfo(
          'Nombre de maryeurs récupérés: ${data.length}',
          context: 'UnifiedUserService.getAllMaryeurs',
        );

        // Journaliser les détails des maryeurs pour le débogage
        if (data.isNotEmpty) {
          ErrorHandler.instance.logInfo(
            'Premier maryeur: ${data.first}',
            context: 'UnifiedUserService.getAllMaryeurs',
          );
        }

        return data.map((item) => Maryeur.fromMap(item)).toList();
      } else if (response.containsKey('data') &&
          response['data'] is Map &&
          response['data'].containsKey('data') &&
          response['data']['data'] is List) {
        final List<dynamic> data = response['data']['data'];

        // Journaliser les détails des maryeurs pour le débogage
        if (data.isNotEmpty) {
          ErrorHandler.instance.logInfo(
            'Premier maryeur (structure imbriquée): ${data.first}',
            context: 'UnifiedUserService.getAllMaryeurs',
          );
        }

        return data.map((item) => Maryeur.fromMap(item)).toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.getAllMaryeurs',
      );
      return [];
    }
  }

  /// Récupère tous les clients
  Future<List<Client>> getAllClients() async {
    try {
      final response = await _apiService.get('clients');
      // Vérifier si les données sont dans data.data (structure imbriquée)
      if (response.containsKey('data') &&
          response['data'] is Map &&
          response['data'].containsKey('data') &&
          response['data']['data'] is List) {
        final List<dynamic> data = response['data']['data'];
        return data.map((item) => Client.fromMap(item)).toList();
      } else if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> data = response['data'];
        return data.map((item) => Client.fromMap(item)).toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.getAllClients',
      );
      return [];
    }
  }

  /// Met à jour le profil de l'utilisateur actuel
  Future<dynamic> updateCurrentUserProfile(
    Map<String, dynamic> userData,
  ) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        throw Exception('Utilisateur non connecté');
      }

      String endpoint;
      String userId = currentUser.id;

      if (currentUser is Pecheur) {
        endpoint = 'pecheurs/$userId';
      } else if (currentUser is Veterinaire) {
        endpoint = 'veterinaires/$userId';
      } else if (currentUser is Maryeur) {
        endpoint = 'maryeurs/$userId';
      } else if (currentUser is Admin) {
        endpoint = 'admins/$userId';
      } else {
        endpoint = 'clients/$userId';
      }

      final response = await _apiService.put(endpoint, userData);
      if (response.containsKey('data')) {
        // Mettre à jour l'utilisateur dans le service d'authentification
        await _authService.saveCurrentUser(await _authService.getCurrentUser());
        return response['data'];
      }
      return null;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.updateCurrentUserProfile',
      );
      rethrow;
    }
  }

  /// Change le mot de passe de l'utilisateur actuel
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        throw Exception('Utilisateur non connecté');
      }

      String endpoint;
      String userId = currentUser.id;

      if (currentUser is Pecheur) {
        endpoint = 'pecheurs/$userId/password';
      } else if (currentUser is Veterinaire) {
        endpoint = 'veterinaires/$userId/password';
      } else if (currentUser is Maryeur) {
        endpoint = 'maryeurs/$userId/password';
      } else {
        endpoint = 'clients/$userId/password';
      }

      await _apiService.put(endpoint, {
        'oldPassword': oldPassword,
        'newPassword': newPassword,
      });

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.changePassword',
      );
      return false;
    }
  }

  /// Met à jour la photo de profil de l'utilisateur actuel
  Future<bool> updateProfilePhoto(File photoFile) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Vérifier que le fichier existe
      if (!await photoFile.exists()) {
        throw Exception('Le fichier n\'existe pas: ${photoFile.path}');
      }

      // Vérifier la taille du fichier
      final fileSize = await photoFile.length();
      if (fileSize > 5 * 1024 * 1024) {
        // 5 MB
        throw Exception('La taille du fichier dépasse la limite de 5 MB');
      }

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Téléchargement de la photo de profil: ${photoFile.path} (${fileSize / 1024} KB)',
        context: 'UnifiedUserService.updateProfilePhoto',
      );

      // Télécharger l'image sur le serveur
      final imageUrl = await _apiService.uploadFile(
        'images/upload',
        photoFile.path,
      );

      if (imageUrl == null) {
        throw Exception('Échec du téléchargement de l\'image');
      }

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Image téléchargée avec succès: $imageUrl',
        context: 'UnifiedUserService.updateProfilePhoto',
      );

      // Mettre à jour le profil avec l'URL de l'image
      String endpoint;
      String userId = currentUser.id;

      if (currentUser is Pecheur) {
        endpoint = 'pecheurs/$userId';
      } else if (currentUser is Veterinaire) {
        endpoint = 'veterinaires/$userId';
      } else if (currentUser is Maryeur) {
        endpoint = 'maryeurs/$userId';
      } else {
        endpoint = 'clients/$userId';
      }

      // Créer un objet avec les données à mettre à jour
      final updateData = {'photo': imageUrl};

      // Mettre à jour le profil
      await _apiService.patch(endpoint, updateData);

      // Rafraîchir les données utilisateur
      await _authService.getCurrentUser();

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedUserService.updateProfilePhoto',
      );
      return false;
    }
  }
}
