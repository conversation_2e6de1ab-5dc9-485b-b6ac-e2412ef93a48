/// Classe de base pour tous les modèles d'utilisateurs
/// Fournit des méthodes communes pour la gestion des IDs et des rôles
abstract class BaseUser {
  /// Champs communs à tous les utilisateurs
  final String? id;
  final String email;
  final String roles;
  final String password;
  final String nom;
  final String prenom;
  final String? telephone;
  final String? photo;
  final bool isValidated;
  final bool isBlocked;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// Constructeur
  BaseUser({
    this.id,
    required this.email,
    required this.roles,
    required this.password,
    required this.nom,
    required this.prenom,
    this.telephone,
    this.photo,
    this.isValidated = false,
    this.isBlocked = false,
    this.createdAt,
    this.updatedAt,
  });

  /// Convertit une chaîne en DateTime
  static DateTime? parseDateTime(String? dateStr) {
    if (dateStr == null) return null;
    try {
      return DateTime.parse(dateStr);
    } catch (e) {
      return null;
    }
  }

  /// Extrait l'ID à partir d'une map, en gérant les différentes variantes possibles
  /// Retourne l'ID standardisé
  static String? extractId(Map<String, dynamic> map) {
    // Utiliser l'ID standardisé (id) si disponible
    if (map.containsKey('id') && map['id'] != null) {
      return map['id'].toString();
    }

    // Fallback sur _id si id n'existe pas
    if (map.containsKey('_id')) {
      // Si _id est un objet avec $oid (format MongoDB)
      if (map['_id'] is Map && map['_id'].containsKey('\$oid')) {
        return map['_id']['\$oid']?.toString();
      }

      // Sinon, utiliser directement la valeur de _id
      return map['_id']?.toString();
    }

    return null;
  }

  /// Vérifie si l'utilisateur a un rôle spécifique
  static bool hasRole(String roles, String role) {
    return roles.contains(role);
  }

  /// Vérifie si l'utilisateur est un pêcheur
  static bool isPecheur(String roles) {
    return hasRole(roles, 'ROLE_PECHEUR');
  }

  /// Vérifie si l'utilisateur est un vétérinaire
  static bool isVeterinaire(String roles) {
    return hasRole(roles, 'ROLE_VETERINAIRE');
  }

  /// Vérifie si l'utilisateur est un mareyeur
  static bool isMaryeur(String roles) {
    return hasRole(roles, 'ROLE_MARYEUR');
  }

  /// Vérifie si l'utilisateur est un client
  static bool isClient(String roles) {
    return hasRole(roles, 'ROLE_CLIENT');
  }

  /// Vérifie si l'utilisateur est un administrateur
  static bool isAdmin(String roles) {
    return hasRole(roles, 'ROLE_ADMIN');
  }

  /// Obtient le type d'utilisateur à partir des rôles
  static String getUserType(String roles) {
    if (isPecheur(roles)) return 'pecheur';
    if (isVeterinaire(roles)) return 'veterinaire';
    if (isMaryeur(roles)) return 'maryeur';
    if (isAdmin(roles)) return 'admin';
    return 'client';
  }

  /// Méthodes d'instance pour vérifier le rôle
  bool isPecheurRole() => isPecheur(roles);
  bool isVeterinaireRole() => isVeterinaire(roles);
  bool isMaryeurRole() => isMaryeur(roles);
  bool isClientRole() => isClient(roles);
  bool isAdminRole() => isAdmin(roles);

  /// Convertit l'objet en map (à implémenter dans les classes dérivées)
  Map<String, dynamic> toMap();
}
