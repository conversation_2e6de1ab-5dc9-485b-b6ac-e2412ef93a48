/// Types d'erreurs spécifiques pour l'application
enum ErrorType {
  network,
  authentication,
  authorization,
  notFound,
  validation,
  server,
  unknown,
}

/// Classe pour représenter une erreur de l'application
class AppError extends Error {
  final String message;
  final ErrorType type;
  final dynamic originalError;
  final dynamic details;
  @override
  final StackTrace? stackTrace;
  final String? context;

  AppError({
    required this.message,
    required this.type,
    this.originalError,
    this.details,
    this.stackTrace,
    this.context,
  });

  @override
  String toString() {
    String result = 'AppError: $message (Type: $type)';
    if (details != null) {
      result += '\nDétails: $details';
    }
    if (context != null) {
      result += '\nContexte: $context';
    }
    return result;
  }
}
