/**
 * Middleware unifié pour la standardisation des données
 * Ce middleware remplace tous les middlewares de standardisation existants
 */

const idService = require('../services/idService');
const standardizationService = require('../services/unifiedStandardizationService');

/**
 * Middleware pour standardiser les données dans les requêtes et les réponses
 * @param {Object} req - Objet requête Express
 * @param {Object} res - Objet réponse Express
 * @param {Function} next - Fonction next Express
 */
function standardizeIds(req, res, next) {
  try {
    // Standardiser les données dans le corps de la requête
    if (req.body && typeof req.body === 'object') {
      // Conserver le corps original pour le débogage
      req.originalBody = JSON.parse(JSON.stringify(req.body));

      // Standardiser les noms de champs et les types de données
      req.body = standardizationService.standardizeForFrontend(req.body);
    }

    // Standardiser les IDs dans les paramètres de la requête
    if (req.params && req.params.id) {
      // Conserver l'ID original pour le débogage
      req.originalId = req.params.id;
    }

    // Remplacer la méthode json de res pour standardiser les données dans les réponses
    const originalJson = res.json;
    res.json = function(data) {
      // Standardiser les données dans la réponse
      const standardizedData = standardizationService.standardizeForFrontend(data);

      // Appeler la méthode json originale avec les données standardisées
      return originalJson.call(this, standardizedData);
    };

    // Ajouter des méthodes utilitaires à req
    req.isValidObjectId = (id) => idService.isValidObjectId(id || req.params.id);
    req.toObjectId = (id) => idService.toObjectId(id || req.params.id);

    next();
  } catch (error) {
    console.error('Erreur lors de la standardisation des données:', error);
    next(error);
  }
}

module.exports = standardizeIds;
