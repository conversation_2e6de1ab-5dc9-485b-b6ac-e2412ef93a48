{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/Android/src/Android/SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Users/<USER>/Android/src/Android/SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Users/<USER>/Android/src/Android/SDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/Users/<USER>/Android/src/Android/SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-468a66485d52a39dc23c.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-acad195ef0d207cc1c14.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-39303bc003817261b828.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-acad195ef0d207cc1c14.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-39303bc003817261b828.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-468a66485d52a39dc23c.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}