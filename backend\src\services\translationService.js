/**
 * Service de traduction pour l'API backend
 * Gère les traductions des messages d'erreur et de succès
 */

const translations = {
  fr: {
    // Messages de succès
    success: {
      operationSuccess: 'Opération réussie',
      resourceCreated: 'Ressource créée avec succès',
      requestAccepted: 'Requête acceptée pour traitement',
      operationSuccessNoContent: 'Opération réussie sans contenu à retourner',
      userAuthenticated: 'Utilisateur authentifié',
      listRetrieved: 'Liste récupérée avec succès',
      profileRetrieved: 'Profil récupéré avec succès',
      passwordChanged: 'Mot de passe mis à jour avec succès',
      accountValidated: 'Compte validé avec succès',
      accountBlocked: 'Compte bloqué avec succès',
      accountUnblocked: 'Compte débloqué avec succès',
      lotApproved: 'Lot approuvé avec succès',
      lotRejected: 'Lot rejeté avec succès',
      auctionStarted: 'Enchère démarrée avec succès',
      bidPlaced: 'Enchère placée avec succès',
      notificationSent: 'Notification envoyée avec succès',
    },
    
    // Messages d'erreur
    error: {
      invalidRequest: 'Requête invalide',
      authenticationRequired: 'Authentification requise',
      accessDenied: 'Accès refusé',
      resourceNotFound: 'Ressource non trouvée',
      conflict: 'Conflit avec l\'état actuel de la ressource',
      unprocessableEntity: 'Entité non traitable',
      internalServerError: 'Erreur interne du serveur',
      serviceUnavailable: 'Service temporairement indisponible',
      generalError: 'Une erreur est survenue',
      
      // Erreurs d'authentification
      tokenMissing: 'Token d\'authentification manquant',
      sessionExpired: 'Session expirée, veuillez vous reconnecter',
      invalidToken: 'Token d\'authentification invalide',
      userNotFound: 'Utilisateur non trouvé',
      incorrectCredentials: 'Email ou mot de passe incorrect',
      accountPending: 'Votre compte est en attente de validation',
      accountBlocked: 'Votre compte a été bloqué',
      
      // Erreurs de validation
      validationError: 'Erreur de validation',
      emailRequired: 'Email requis',
      passwordRequired: 'Mot de passe requis',
      roleRequired: 'Rôle requis',
      invalidRole: 'Rôle invalide ou non autorisé',
      passwordTooShort: 'Le mot de passe doit contenir au moins 6 caractères',
      emailExists: 'Cet email est déjà utilisé',
      currentPasswordIncorrect: 'Mot de passe actuel incorrect',
      
      // Erreurs de base de données
      databaseConnection: 'Erreur de connexion à la base de données',
      duplicateKey: 'Conflit de données',
      
      // Erreurs métier
      lotNotFound: 'Lot non trouvé',
      lotAlreadyProcessed: 'Ce lot a déjà été traité',
      auctionNotFound: 'Enchère non trouvée',
      auctionEnded: 'Cette enchère est terminée',
      bidTooLow: 'Votre enchère est trop faible',
      notificationFailed: 'Échec de l\'envoi de la notification',
    }
  },
  
  ar: {
    // Messages de succès
    success: {
      operationSuccess: 'تمت العملية بنجاح',
      resourceCreated: 'تم إنشاء المورد بنجاح',
      requestAccepted: 'تم قبول الطلب للمعالجة',
      operationSuccessNoContent: 'تمت العملية بنجاح بدون محتوى للإرجاع',
      userAuthenticated: 'تم التحقق من المستخدم',
      listRetrieved: 'تم استرداد القائمة بنجاح',
      profileRetrieved: 'تم استرداد الملف الشخصي بنجاح',
      passwordChanged: 'تم تحديث كلمة المرور بنجاح',
      accountValidated: 'تم التحقق من الحساب بنجاح',
      accountBlocked: 'تم حظر الحساب بنجاح',
      accountUnblocked: 'تم إلغاء حظر الحساب بنجاح',
      lotApproved: 'تم الموافقة على الدفعة بنجاح',
      lotRejected: 'تم رفض الدفعة بنجاح',
      auctionStarted: 'تم بدء المزاد بنجاح',
      bidPlaced: 'تم وضع المزايدة بنجاح',
      notificationSent: 'تم إرسال الإشعار بنجاح',
    },
    
    // Messages d'erreur
    error: {
      invalidRequest: 'طلب غير صالح',
      authenticationRequired: 'مطلوب التحقق من الهوية',
      accessDenied: 'تم رفض الوصول',
      resourceNotFound: 'المورد غير موجود',
      conflict: 'تعارض مع الحالة الحالية للمورد',
      unprocessableEntity: 'كيان غير قابل للمعالجة',
      internalServerError: 'خطأ داخلي في الخادم',
      serviceUnavailable: 'الخدمة غير متاحة مؤقتاً',
      generalError: 'حدث خطأ',
      
      // Erreurs d'authentification
      tokenMissing: 'رمز المصادقة مفقود',
      sessionExpired: 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
      invalidToken: 'رمز المصادقة غير صالح',
      userNotFound: 'المستخدم غير موجود',
      incorrectCredentials: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
      accountPending: 'حسابك في انتظار التحقق',
      accountBlocked: 'تم حظر حسابك',
      
      // Erreurs de validation
      validationError: 'خطأ في التحقق',
      emailRequired: 'البريد الإلكتروني مطلوب',
      passwordRequired: 'كلمة المرور مطلوبة',
      roleRequired: 'الدور مطلوب',
      invalidRole: 'دور غير صالح أو غير مصرح به',
      passwordTooShort: 'يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل',
      emailExists: 'هذا البريد الإلكتروني مستخدم بالفعل',
      currentPasswordIncorrect: 'كلمة المرور الحالية غير صحيحة',
      
      // Erreurs de base de données
      databaseConnection: 'خطأ في الاتصال بقاعدة البيانات',
      duplicateKey: 'تعارض في البيانات',
      
      // Erreurs métier
      lotNotFound: 'الدفعة غير موجودة',
      lotAlreadyProcessed: 'تم معالجة هذه الدفعة بالفعل',
      auctionNotFound: 'المزاد غير موجود',
      auctionEnded: 'انتهى هذا المزاد',
      bidTooLow: 'مزايدتك منخفضة جداً',
      notificationFailed: 'فشل في إرسال الإشعار',
    }
  }
};

/**
 * Service de traduction
 */
class TranslationService {
  /**
   * Obtient une traduction pour une clé donnée
   * @param {string} key - Clé de traduction (ex: 'success.operationSuccess')
   * @param {string} lang - Code de langue ('fr' ou 'ar')
   * @param {Object} params - Paramètres pour l'interpolation
   * @returns {string} - Texte traduit
   */
  static translate(key, lang = 'fr', params = {}) {
    // Vérifier si la langue est supportée
    if (!translations[lang]) {
      lang = 'fr'; // Fallback vers le français
    }
    
    // Naviguer dans l'objet de traductions
    const keys = key.split('.');
    let translation = translations[lang];
    
    for (const k of keys) {
      if (translation && typeof translation === 'object' && translation[k]) {
        translation = translation[k];
      } else {
        // Fallback vers le français si la traduction n'existe pas
        translation = translations.fr;
        for (const fallbackKey of keys) {
          if (translation && typeof translation === 'object' && translation[fallbackKey]) {
            translation = translation[fallbackKey];
          } else {
            return key; // Retourner la clé si aucune traduction n'est trouvée
          }
        }
        break;
      }
    }
    
    // Si la traduction est une chaîne, effectuer l'interpolation
    if (typeof translation === 'string') {
      return this.interpolate(translation, params);
    }
    
    return key; // Retourner la clé si aucune traduction valide n'est trouvée
  }
  
  /**
   * Effectue l'interpolation des paramètres dans une chaîne
   * @param {string} text - Texte avec des placeholders
   * @param {Object} params - Paramètres à interpoler
   * @returns {string} - Texte avec les paramètres interpolés
   */
  static interpolate(text, params) {
    return text.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }
  
  /**
   * Obtient la langue à partir des headers de la requête
   * @param {Object} req - Objet requête Express
   * @returns {string} - Code de langue
   */
  static getLanguageFromRequest(req) {
    // Vérifier le header Accept-Language
    const acceptLanguage = req.headers['accept-language'];
    if (acceptLanguage) {
      if (acceptLanguage.includes('ar')) return 'ar';
      if (acceptLanguage.includes('fr')) return 'fr';
    }
    
    // Vérifier un header personnalisé
    const customLang = req.headers['x-language'] || req.headers['x-lang'];
    if (customLang && ['fr', 'ar'].includes(customLang)) {
      return customLang;
    }
    
    // Vérifier les paramètres de requête
    const queryLang = req.query.lang;
    if (queryLang && ['fr', 'ar'].includes(queryLang)) {
      return queryLang;
    }
    
    // Français par défaut
    return 'fr';
  }
}

module.exports = TranslationService;
