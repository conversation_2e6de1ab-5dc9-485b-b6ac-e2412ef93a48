import 'dart:io';
import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_profile_service.dart';
import 'package:seatrace/services/unified_user_service.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/client.dart';
import 'package:seatrace/utils/validators.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/screens/login_screen.dart';
import 'package:seatrace/widgets/sea_widgets.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

class ProfileScreenNew extends StatefulWidget {
  const ProfileScreenNew({super.key});

  @override
  State<ProfileScreenNew> createState() => ProfileScreenNewState();
}

class ProfileScreenNewState extends State<ProfileScreenNew> {
  final _formKey = GlobalKey<FormState>();

  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isLoading = true;
  bool _isSaving = false;
  bool _isChangingPassword = false;
  bool _isUploadingPhoto = false;
  String? _errorMessage;
  String? _successMessage;

  Map<String, dynamic>? _userData;
  String _userType = '';
  // _photoPath est utilisé pour stocker le chemin de la photo dans le système de fichiers
  // _photoUrl est utilisé pour afficher la photo depuis le serveur
  String? _photoUrl;

  final _animationService = AnimationService();
  final _responsiveService = ResponsiveService();
  final _profileService = UnifiedProfileService();
  final _userService = UnifiedUserService();
  final _apiService = UnifiedApiService();
  final _authService = UnifiedAuthService();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nomController.dispose();
    _prenomController.dispose();
    _telephoneController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await _authService.getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Log pour déboguer
      debugPrint(
        'Utilisateur récupéré: id=${user.id}, nom=${user.nom}, prenom=${user.prenom}, telephone=${user.telephone}',
      );

      // Déterminer le type d'utilisateur
      Map<String, dynamic> userData = {};

      if (user is Pecheur) {
        userData = user.toMap();
        _userType = 'Pêcheur';
      } else if (user is Veterinaire) {
        userData = user.toMap();
        _userType = 'Vétérinaire';
      } else if (user is Maryeur) {
        userData = user.toMap();
        _userType = 'Maryeur';
      } else if (user is Client) {
        userData = user.toMap();
        _userType = 'Client';
      } else {
        // Vérifier si c'est un administrateur via les données utilisateur
        userData = user.toMap();
        if (userData['roles'] != null &&
            userData['roles'].toString().contains('ROLE_ADMIN')) {
          _userType = 'Administrateur';
        } else {
          _userType = 'Client'; // Par défaut
        }
      }

      // Log pour déboguer
      debugPrint('Détails utilisateur: ${userData.toString()}');

      // Créer un objet utilisateur complet en combinant les données de base et les détails
      final Map<String, dynamic> completeUserData = {
        'id': user.id,
        'nom': user.nom,
        'prenom': user.prenom,
        'telephone': user.telephone,
        'photo': user.photo,
        'email': user.email,
      };

      // Mettre à jour les informations de base si elles sont disponibles dans les détails
      if (userData['nom'] != null && userData['nom'].toString().isNotEmpty) {
        completeUserData['nom'] = userData['nom'];
      }
      if (userData['prenom'] != null &&
          userData['prenom'].toString().isNotEmpty) {
        completeUserData['prenom'] = userData['prenom'];
      }
      if (userData['telephone'] != null &&
          userData['telephone'].toString().isNotEmpty) {
        completeUserData['telephone'] = userData['telephone'];
      }
      if (userData['photo'] != null &&
          userData['photo'].toString().isNotEmpty) {
        completeUserData['photo'] = userData['photo'];
      }
      if (userData['email'] != null &&
          userData['email'].toString().isNotEmpty) {
        completeUserData['email'] = userData['email'];
      }

      // Ajouter les champs spécifiques au type d'utilisateur
      if (user.isPecheur()) {
        completeUserData['matricule'] = userData['matricule'];
        completeUserData['bateau'] = userData['bateau'];
        completeUserData['port'] = userData['port'];
        completeUserData['cin'] = userData['cin'];
      } else if (user.isMaryeur()) {
        completeUserData['societe'] = userData['societe'];
        completeUserData['registre'] = userData['registre'];
        completeUserData['adresse'] = userData['adresse'];
      } else if (user.isVeterinaire()) {
        completeUserData['specialite'] = userData['specialite'];
        completeUserData['licence'] = userData['licence'];
        completeUserData['adresse'] = userData['adresse'];
      } else if (user.isClient()) {
        completeUserData['adresse'] = userData['adresse'];
      }

      debugPrint('Données utilisateur complètes: $completeUserData');

      // Préparer l'URL de la photo si disponible
      String? photoUrl;
      if (completeUserData['photo'] != null &&
          completeUserData['photo'].toString().isNotEmpty) {
        // Construire l'URL de l'image à partir de l'URL de base de l'API
        final baseUrl = 'http://localhost:3000/api';
        photoUrl =
            '$baseUrl/images/uploads/${completeUserData['photo'].toString()}';
      }

      setState(() {
        _userData = completeUserData;
        _nomController.text = completeUserData['nom'] ?? '';
        _prenomController.text = completeUserData['prenom'] ?? '';
        _telephoneController.text =
            completeUserData['telephone']?.toString() ?? '';
        // Stocker l'URL de la photo
        _photoUrl = photoUrl;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Erreur lors du chargement des données utilisateur: $e');

      // Essayer de récupérer les données de base de l'utilisateur
      try {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          setState(() {
            _userData = {
              'id': user.id,
              'nom': user.nom,
              'prenom': user.prenom,
              'telephone': user.telephone,
              'photo': user.photo,
              'email': user.email,
            };

            _nomController.text = user.nom;
            _prenomController.text = user.prenom;
            _telephoneController.text = user.telephone ?? '';
            // Construire l'URL de la photo
            if (user.photo != null && user.photo!.isNotEmpty) {
              final baseUrl = 'http://localhost:3000/api';
              _photoUrl = '$baseUrl/images/uploads/${user.photo}';
            }
            _isLoading = false;
          });
          return;
        }
      } catch (secondError) {
        debugPrint('Erreur secondaire: $secondError');
      }

      setState(() {
        _errorMessage = 'Erreur lors du chargement: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _showImageSourceDialog() async {
    // Tester la connexion au serveur avant d'afficher le dialogue
    setState(() {
      _isUploadingPhoto = true;
      _errorMessage = null;
      _successMessage = 'Vérification de la connexion...';
    });

    try {
      // Vérifier la connexion en faisant une requête simple
      await _apiService.get('health');

      setState(() {
        _isUploadingPhoto = false;
        _successMessage = null;
      });
    } catch (e) {
      setState(() {
        _isUploadingPhoto = false;
        _successMessage = null;
        _errorMessage =
            'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
      });
      return;
    }

    if (!mounted) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Choisir une source'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SeaListItem(
                  title: 'Appareil photo',
                  icon: Icons.camera_alt,
                  onTap: () {
                    Navigator.of(context).pop();
                    _pickImage(ImageSource.camera);
                  },
                  margin: const EdgeInsets.only(bottom: 8),
                ),
                SeaListItem(
                  title: 'Galerie',
                  icon: Icons.photo_library,
                  onTap: () {
                    Navigator.of(context).pop();
                    _pickImage(ImageSource.gallery);
                  },
                ),
              ],
            ),
            actions: [
              SeaButton.text(
                text: 'Annuler',
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      setState(() {
        _isUploadingPhoto = true;
        _errorMessage = null;
        _successMessage = null;
      });

      // Sélectionner l'image
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
        requestFullMetadata:
            false, // Réduire les métadonnées pour alléger le fichier
      );

      if (pickedFile == null) {
        // L'utilisateur a annulé la sélection
        debugPrint('Sélection d\'image annulée par l\'utilisateur');
        setState(() {
          _isUploadingPhoto = false;
        });
        return;
      }

      // Vérifier l'extension du fichier
      final ext = path.extension(pickedFile.path).toLowerCase();
      final validExtensions = [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.webp',
        '.heic',
      ];
      if (!validExtensions.contains(ext)) {
        debugPrint('Extension de fichier non supportée: $ext');
        setState(() {
          _errorMessage =
              'Format d\'image non supporté. Utilisez JPG, PNG, GIF ou WebP.';
          _isUploadingPhoto = false;
        });
        return;
      }

      // Vérifier si l'utilisateur est connecté
      final user = await _authService.getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Préparer le fichier image
      final imageFile = File(pickedFile.path);
      debugPrint('Image sélectionnée: ${imageFile.path}');

      // Vérifier la taille du fichier
      final fileSize = await imageFile.length();
      debugPrint('Taille de l\'image: ${fileSize / 1024} KB');

      // Télécharger l'image sur le serveur avec gestion des erreurs
      debugPrint('Début du téléchargement de l\'image...');

      // Afficher un message de progression
      setState(() {
        _successMessage = 'Téléchargement en cours...';
      });

      // Utiliser le service utilisateur pour télécharger et mettre à jour la photo de profil
      final success = await _userService.updateProfilePhoto(imageFile);

      if (!success) {
        throw Exception(
          'Échec du téléchargement ou de la mise à jour de la photo de profil',
        );
      }

      debugPrint('Photo de profil mise à jour avec succès');

      // Afficher un message de progression
      setState(() {
        _successMessage = 'Photo mise à jour avec succès';
      });

      // Rafraîchir les données utilisateur
      await _loadUserData();

      setState(() {
        _successMessage = 'Photo de profil mise à jour avec succès';
        _isUploadingPhoto = false;
      });
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour de la photo: $e');

      // Message d'erreur plus convivial
      String errorMessage = 'Erreur lors de la mise à jour de la photo';

      if (e.toString().contains('taille')) {
        errorMessage = 'La taille de l\'image est trop grande. Maximum 5 MB.';
      } else if (e.toString().contains('connexion') ||
          e.toString().contains('network') ||
          e.toString().contains('timeout')) {
        errorMessage =
            'Problème de connexion au serveur. Vérifiez votre connexion internet.';
      } else if (e.toString().contains('format') ||
          e.toString().contains('extension') ||
          e.toString().contains('type')) {
        errorMessage =
            'Format d\'image non supporté. Utilisez JPG, PNG ou GIF.';
      } else if (e.toString().contains('token') ||
          e.toString().contains('authentification') ||
          e.toString().contains('connecté')) {
        errorMessage = 'Vous devez être connecté pour télécharger une image.';
      } else if (e.toString().contains('permission') ||
          e.toString().contains('accès')) {
        errorMessage =
            'Problème de permission. Veuillez autoriser l\'accès à la caméra et aux photos.';
      }

      setState(() {
        _errorMessage = errorMessage;
        _successMessage = null;
        _isUploadingPhoto = false;
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final updatedData = {
        'nom': _nomController.text.trim(),
        'prenom': _prenomController.text.trim(),
        'telephone': _telephoneController.text.trim(),
      };

      // Utiliser le service de profil unifié pour mettre à jour le profil
      final success = await _profileService.updateProfile(updatedData);

      if (!success) {
        throw Exception('Échec de la mise à jour du profil');
      }

      // Recharger les données utilisateur pour mettre à jour l'interface
      await _loadUserData();

      setState(() {
        _successMessage = 'Profil mis à jour avec succès';
        _isSaving = false;
      });
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du profil: $e');

      // Message d'erreur plus convivial
      String errorMessage = 'Erreur lors de la mise à jour du profil';

      if (e.toString().contains('connexion') ||
          e.toString().contains('network')) {
        errorMessage =
            'Problème de connexion au serveur. Vérifiez votre connexion internet';
      } else if (e.toString().contains('non trouvé') ||
          e.toString().contains('not found')) {
        errorMessage = 'Utilisateur non trouvé. Veuillez vous reconnecter';
      } else if (e.toString().contains('autorisation') ||
          e.toString().contains('authorization')) {
        errorMessage = 'Vous n\'êtes pas autorisé à effectuer cette action';
      }

      setState(() {
        _errorMessage = errorMessage;
        _isSaving = false;
      });
    }
  }

  Future<void> _changePassword() async {
    if (_currentPasswordController.text.isEmpty ||
        _newPasswordController.text.isEmpty ||
        _confirmPasswordController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Veuillez remplir tous les champs';
      });
      return;
    }

    if (_newPasswordController.text != _confirmPasswordController.text) {
      setState(() {
        _errorMessage = 'Les nouveaux mots de passe ne correspondent pas';
      });
      return;
    }

    // Vérifier que le nouveau mot de passe est assez long
    if (_newPasswordController.text.length < 6) {
      setState(() {
        _errorMessage =
            'Le nouveau mot de passe doit contenir au moins 6 caractères';
      });
      return;
    }

    setState(() {
      _isChangingPassword = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Utiliser le service de profil unifié pour changer le mot de passe
      final success = await _profileService.changePassword(
        _currentPasswordController.text,
        _newPasswordController.text,
      );

      if (!success) {
        throw Exception('Échec du changement de mot de passe');
      }

      // Effacer les champs
      _currentPasswordController.clear();
      _newPasswordController.clear();
      _confirmPasswordController.clear();

      setState(() {
        _successMessage = 'Mot de passe mis à jour avec succès';
        _isChangingPassword = false;
      });
    } catch (e) {
      debugPrint('Erreur lors du changement de mot de passe: $e');

      // Message d'erreur plus convivial
      String errorMessage = 'Erreur lors du changement de mot de passe';

      if (e.toString().contains('mot de passe actuel') ||
          e.toString().contains('current password') ||
          e.toString().contains('incorrect')) {
        errorMessage = 'Le mot de passe actuel est incorrect';
      } else if (e.toString().contains('connexion') ||
          e.toString().contains('network')) {
        errorMessage =
            'Problème de connexion au serveur. Vérifiez votre connexion internet';
      } else if (e.toString().contains('non trouvé') ||
          e.toString().contains('not found')) {
        errorMessage = 'Utilisateur non trouvé. Veuillez vous reconnecter';
      }

      setState(() {
        _errorMessage = errorMessage;
        _isChangingPassword = false;
      });
    }
  }

  // Méthode pour obtenir les initiales de l'utilisateur
  String _getInitials() {
    if (_userData == null) return '?';

    final prenom = _userData!['prenom'] as String?;
    final nom = _userData!['nom'] as String?;

    String initials = '';

    if (prenom != null && prenom.isNotEmpty) {
      initials += prenom[0];
    }

    if (nom != null && nom.isNotEmpty) {
      initials += nom[0];
    }

    return initials.isEmpty ? '?' : initials;
  }

  Future<void> _logout() async {
    await _authService.logout();
    if (!mounted) return;
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (_) => const LoginScreen()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mon profil'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Déconnexion',
          ),
        ],
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null && _userData == null
                ? _animationService.fadeIn(
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage!,
                          style: TextStyle(color: theme.colorScheme.error),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        SeaButton.primary(
                          text: 'Réessayer',
                          icon: Icons.refresh,
                          onPressed: _loadUserData,
                        ),
                      ],
                    ),
                  ),
                )
                : SingleChildScrollView(
                  padding: _responsiveService.adaptivePadding(context),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _animationService.staggeredList([
                      // En-tête du profil avec photo
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              // Nom et prénom
                              if (_userData != null)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 16.0),
                                  child: Text(
                                    'Bienvenue, ${_userData!['prenom']} ${_userData!['nom']}',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),

                              // Photo de profil
                              Stack(
                                alignment: Alignment.bottomRight,
                                children: [
                                  GestureDetector(
                                    onTap:
                                        _isUploadingPhoto
                                            ? null
                                            : _showImageSourceDialog,
                                    child:
                                        _isUploadingPhoto
                                            ? const SizedBox(
                                              width: 120,
                                              height: 120,
                                              child:
                                                  CircularProgressIndicator(),
                                            )
                                            : CircleAvatar(
                                              radius: 60,
                                              backgroundColor: primaryColor
                                                  .withValues(alpha: 0.1),
                                              foregroundColor: primaryColor,
                                              backgroundImage:
                                                  _photoUrl != null
                                                      ? NetworkImage(_photoUrl!)
                                                      : null,
                                              child:
                                                  _photoUrl == null
                                                      ? Text(
                                                        _getInitials(),
                                                        style: const TextStyle(
                                                          fontSize: 40,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      )
                                                      : null,
                                            ),
                                  ),
                                  if (!_isUploadingPhoto)
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: primaryColor,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.1,
                                            ),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: IconButton(
                                        icon: const Icon(
                                          Icons.camera_alt,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                        onPressed: _showImageSourceDialog,
                                        tooltip: 'Changer la photo',
                                        constraints: const BoxConstraints(
                                          minWidth: 24,
                                          minHeight: 24,
                                        ),
                                        padding: EdgeInsets.zero,
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Informations du profil
                      SeaCard(
                        title: 'Informations personnelles',
                        icon: Icons.person,
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              // Nom
                              TextFormField(
                                controller: _nomController,
                                decoration: const InputDecoration(
                                  labelText: 'Nom',
                                  prefixIcon: Icon(Icons.person_outline),
                                ),
                                validator: Validators.required(
                                  'Le nom est requis',
                                ),
                                enabled: !_isSaving,
                              ),
                              const SizedBox(height: 16),

                              // Prénom
                              TextFormField(
                                controller: _prenomController,
                                decoration: const InputDecoration(
                                  labelText: 'Prénom',
                                  prefixIcon: Icon(Icons.person_outline),
                                ),
                                validator: Validators.required(
                                  'Le prénom est requis',
                                ),
                                enabled: !_isSaving,
                              ),
                              const SizedBox(height: 16),

                              // Téléphone
                              TextFormField(
                                controller: _telephoneController,
                                decoration: const InputDecoration(
                                  labelText: 'Téléphone',
                                  prefixIcon: Icon(Icons.phone),
                                ),
                                keyboardType: TextInputType.phone,
                                validator: Validators.validatePhone,
                                enabled: !_isSaving,
                              ),
                              const SizedBox(height: 16),

                              // Type d'utilisateur
                              if (_userType.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 16.0),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.badge, size: 20),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Type de compte: $_userType',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // Bouton de sauvegarde
                              ElevatedButton.icon(
                                onPressed: _isSaving ? null : _saveProfile,
                                icon:
                                    _isSaving
                                        ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white,
                                          ),
                                        )
                                        : const Icon(Icons.save),
                                label: const Text(
                                  'Enregistrer les modifications',
                                ),
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size.fromHeight(50),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Changement de mot de passe
                      SeaCard(
                        title: 'Changer le mot de passe',
                        icon: Icons.lock,
                        child: Column(
                          children: [
                            // Mot de passe actuel
                            TextFormField(
                              controller: _currentPasswordController,
                              decoration: const InputDecoration(
                                labelText: 'Mot de passe actuel',
                                prefixIcon: Icon(Icons.lock_outline),
                              ),
                              obscureText: true,
                              enabled: !_isChangingPassword,
                            ),
                            const SizedBox(height: 16),

                            // Nouveau mot de passe
                            TextFormField(
                              controller: _newPasswordController,
                              decoration: const InputDecoration(
                                labelText: 'Nouveau mot de passe',
                                prefixIcon: Icon(Icons.lock),
                              ),
                              obscureText: true,
                              enabled: !_isChangingPassword,
                            ),
                            const SizedBox(height: 16),

                            // Confirmation du nouveau mot de passe
                            TextFormField(
                              controller: _confirmPasswordController,
                              decoration: const InputDecoration(
                                labelText: 'Confirmer le nouveau mot de passe',
                                prefixIcon: Icon(Icons.lock),
                              ),
                              obscureText: true,
                              enabled: !_isChangingPassword,
                            ),
                            const SizedBox(height: 16),

                            // Bouton de changement de mot de passe
                            ElevatedButton.icon(
                              onPressed:
                                  _isChangingPassword ? null : _changePassword,
                              icon:
                                  _isChangingPassword
                                      ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.white,
                                        ),
                                      )
                                      : const Icon(Icons.lock_reset),
                              label: const Text('Changer le mot de passe'),
                              style: ElevatedButton.styleFrom(
                                minimumSize: const Size.fromHeight(50),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Messages d'erreur ou de succès
                      if (_errorMessage != null || _successMessage != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child:
                              _errorMessage != null
                                  ? Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Colors.red.shade200,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.error_outline,
                                          color: Colors.red.shade700,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            _errorMessage!,
                                            style: TextStyle(
                                              color: Colors.red.shade700,
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.close),
                                          onPressed:
                                              () => setState(
                                                () => _errorMessage = null,
                                              ),
                                          color: Colors.red.shade700,
                                          iconSize: 20,
                                        ),
                                      ],
                                    ),
                                  )
                                  : Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Colors.green.shade200,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.check_circle_outline,
                                          color: Colors.green.shade700,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            _successMessage!,
                                            style: TextStyle(
                                              color: Colors.green.shade700,
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.close),
                                          onPressed:
                                              () => setState(
                                                () => _successMessage = null,
                                              ),
                                          color: Colors.green.shade700,
                                          iconSize: 20,
                                        ),
                                      ],
                                    ),
                                  ),
                        ),
                    ]),
                  ),
                ),
      ),
    );
  }
}
