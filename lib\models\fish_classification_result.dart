/// <PERSON><PERSON><PERSON><PERSON> représentant le résultat d'une classification de poisson
/// Standardisé pour être cohérent avec le backend
class FishClassificationResult {
  /// Espèce identifiée
  /// Dans le frontend, on utilise 'espece' mais le backend utilise 'nom'
  final String espece;

  /// Niveau de confiance (0.0 à 1.0)
  final double confiance;

  /// Source de la classification (TensorFlow, Google Vision, etc.)
  final String source;

  /// Résultats alternatifs (autres espèces possibles)
  final List<FishClassificationResult>? alternatives;

  /// Getter pour la compatibilité avec le backend qui utilise 'nom'
  String get nom => espece;

  /// Nom scientifique de l'espèce (optionnel)
  final String? nomScientifique;

  FishClassificationResult({
    required this.espece,
    required this.confiance,
    required this.source,
    this.alternatives,
    this.nomScientifique,
  });

  /// Crée une instance à partir d'une map
  factory FishClassificationResult.fromMap(Map<String, dynamic> map) {
    List<FishClassificationResult>? alternatives;

    if (map['alternatives'] != null) {
      alternatives = List<FishClassificationResult>.from(
        (map['alternatives'] as List).map(
          (x) => FishClassificationResult.fromMap(x),
        ),
      );
    }

    return FishClassificationResult(
      // Accepter 'nom' pour la compatibilité avec le backend
      espece: map['espece'] ?? map['nom'] ?? '',
      confiance: _parseDouble(map['confiance']) ?? 1.0,
      source: map['source'] ?? 'direct_classification',
      alternatives: alternatives,
      // Ajouter le nom scientifique pour la compatibilité avec le backend
      nomScientifique: map['nomScientifique'] ?? map['nom_scientifique'],
    );
  }

  /// Méthode utilitaire pour parser les doubles
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Convertit l'instance en map
  /// Inclut à la fois les champs du frontend et du backend pour la compatibilité
  Map<String, dynamic> toMap() {
    return {
      // Champs utilisés par le frontend
      'espece': espece,
      'confiance': confiance,
      'source': source,
      'alternatives': alternatives?.map((x) => x.toMap()).toList(),

      // Champs utilisés par le backend
      'nom': espece, // Le backend utilise 'nom' au lieu de 'espece'
      'nomScientifique': nomScientifique,
    };
  }

  @override
  String toString() {
    return 'FishClassificationResult(espece: $espece, nomScientifique: $nomScientifique, confiance: $confiance, source: $source)';
  }
}
