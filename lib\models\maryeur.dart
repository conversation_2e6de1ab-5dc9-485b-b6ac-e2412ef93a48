import 'package:seatrace/models/base_user.dart';

/// <PERSON><PERSON><PERSON><PERSON> représentant un maryeur dans l'application
/// Correspond au modèle <PERSON> côté backend
class <PERSON><PERSON> extends BaseUser {
  final String? cin;
  final String? matricule;
  final String? port;
  final String? adresse;
  final String? societe;

  Mary<PERSON>({
    super.id,
    required super.email,
    required super.roles,
    required super.password,
    required super.nom,
    required super.prenom,
    super.telephone,
    super.photo,
    super.isValidated = false,
    super.isBlocked = false,
    super.createdAt,
    super.updatedAt,
    this.cin,
    this.matricule,
    this.port,
    this.adresse,
    this.societe,
  });

  factory Maryeur.fromMap(Map<String, dynamic> map) {
    return Mary<PERSON>(
      id: map['_id']?.toString() ?? map['id']?.toString(),
      email: map['email'] ?? '',
      roles: map['roles'] ?? '',
      password: map['password'] ?? '',
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      cin: map['cin'],
      matricule: map['matricule'],
      port: map['port'],
      telephone: map['telephone']?.toString(),
      photo: map['photo'],
      adresse: map['adresse'],
      societe: map['societe'],
      isValidated: map['isValidated'] ?? map['isValid'] ?? false,
      isBlocked: map['isBlocked'] ?? false,
      createdAt: BaseUser.parseDateTime(map['createdAt']),
      updatedAt: BaseUser.parseDateTime(map['updatedAt']),
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'roles': roles,
      'password': password,
      'nom': nom,
      'prenom': prenom,
      'cin': cin,
      'matricule': matricule,
      'port': port,
      'telephone': telephone,
      'photo': photo,
      'adresse': adresse,
      'societe': societe,
      'isValidated': isValidated,
      'isBlocked': isBlocked,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    }..removeWhere((key, value) => value == null);
  }

  /// Créer une copie de l'objet avec des modifications
  Maryeur copyWith({
    String? id,
    String? email,
    String? roles,
    String? password,
    String? nom,
    String? prenom,
    String? cin,
    String? matricule,
    String? port,
    String? telephone,
    String? photo,
    String? adresse,
    String? societe,
    bool? isValidated,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Maryeur(
      id: id ?? this.id,
      email: email ?? this.email,
      roles: roles ?? this.roles,
      password: password ?? this.password,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      cin: cin ?? this.cin,
      matricule: matricule ?? this.matricule,
      port: port ?? this.port,
      telephone: telephone ?? this.telephone,
      photo: photo ?? this.photo,
      adresse: adresse ?? this.adresse,
      societe: societe ?? this.societe,
      isValidated: isValidated ?? this.isValidated,
      isBlocked: isBlocked ?? this.isBlocked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
