// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'SeaTrace';

  @override
  String get loginTitle => 'Login';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get serverIp => 'Server IP Address';

  @override
  String get testConnection => 'Test Connection';

  @override
  String get applyIp => 'Apply';

  @override
  String get discoverServers => 'Discover Servers';

  @override
  String welcomeBack(Object name) {
    return 'Welcome back, $name';
  }

  @override
  String get dashboard => 'Dashboard';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get logout => 'Logout';

  @override
  String get notifications => 'Notifications';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get language => 'Language';

  @override
  String get french => 'French';

  @override
  String get arabic => 'Arabic';

  @override
  String get chooseLanguage => 'Choose Language';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get loading => 'Loading...';

  @override
  String get retry => 'Retry';

  @override
  String get noData => 'No data available';

  @override
  String get scanFish => 'Scan Fish';

  @override
  String get fishDetails => 'Fish Details';

  @override
  String get weight => 'Weight';

  @override
  String get temperature => 'Temperature';

  @override
  String get fishingMethod => 'Fishing Method';

  @override
  String get fishingZone => 'Fishing Zone';

  @override
  String get location => 'Location';

  @override
  String get selectMareyeur => 'Select Mareyeur';

  @override
  String get selectVeterinarian => 'Select Veterinarian';

  @override
  String get submit => 'Submit';

  @override
  String get pendingLots => 'Pending Lots';

  @override
  String get approvedLots => 'Approved Lots';

  @override
  String get rejectedLots => 'Rejected Lots';

  @override
  String get viewPendingLots => 'View Pending Lots';

  @override
  String get statistics => 'Statistics';

  @override
  String get recentActivity => 'Recent Activity';

  @override
  String get viewAll => 'View All';

  @override
  String get noRecentActivity => 'No recent activity';

  @override
  String get approve => 'Approve';

  @override
  String get reject => 'Reject';

  @override
  String get rejectionReason => 'Rejection Reason';

  @override
  String get initialPrice => 'Initial Price';

  @override
  String get minimalPrice => 'Minimal Price';

  @override
  String get startAuction => 'Start Auction';

  @override
  String get activeAuctions => 'Active Auctions';

  @override
  String get completedAuctions => 'Completed Auctions';

  @override
  String get availableAuctions => 'Available Auctions';

  @override
  String get myPurchases => 'My Purchases';

  @override
  String get bid => 'Bid';

  @override
  String get currentBid => 'Current Bid';

  @override
  String get timeRemaining => 'Time Remaining';

  @override
  String get extendAuction => 'Extend Auction';

  @override
  String get closeAuction => 'Close Auction';

  @override
  String get auctionClosed => 'Auction Closed';

  @override
  String get youWon => 'You won this auction!';

  @override
  String get confirmSpecies => 'Confirm this species';

  @override
  String get refreshCounters => 'Refresh Counters';

  @override
  String get countersRefreshed => 'Counters refreshed successfully';

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get languageChanged => 'Language changed successfully';
}
