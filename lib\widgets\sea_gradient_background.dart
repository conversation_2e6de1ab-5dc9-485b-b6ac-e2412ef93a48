import 'package:flutter/material.dart';
import 'package:seatrace/utils/app_theme.dart';

/// Types de dégradés disponibles
enum GradientType {
  /// Dégradé bleu océan
  ocean,

  /// Dégradé turquoise
  turquoise,

  /// Dégradé coucher de soleil
  sunset,

  /// Dégradé nuit
  night,
}

/// Widget d'arrière-plan avec dégradé pour l'application SeaTrace
class SeaGradientBackground extends StatelessWidget {
  /// Le contenu à afficher sur l'arrière-plan
  final Widget child;

  /// Le type de dégradé à utiliser
  final GradientType type;

  /// L'opacité du dégradé (0.0 - 1.0)
  final double opacity;

  /// Crée un nouvel arrière-plan avec dégradé.
  const SeaGradientBackground({
    super.key,
    required this.child,
    this.type = GradientType.ocean,
    this.opacity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: _getGradient(),
      ),
      child: child,
    );
  }

  /// Retourne le dégradé en fonction du type
  LinearGradient _getGradient() {
    switch (type) {
      case GradientType.ocean:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1 * opacity),
            AppTheme.secondaryColor.withValues(alpha: 0.05 * opacity),
          ],
        );
      case GradientType.turquoise:
        return LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.accentColor.withValues(alpha: 0.1 * opacity),
            AppTheme.secondaryColor.withValues(alpha: 0.05 * opacity),
          ],
        );
      case GradientType.sunset:
        return LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            AppTheme.tertiaryColor.withValues(alpha: 0.1 * opacity),
            AppTheme.accentColor.withValues(alpha: 0.05 * opacity),
          ],
        );
      case GradientType.night:
        return LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.quaternaryColor.withValues(alpha: 0.15 * opacity),
            Colors.black.withValues(alpha: 0.05 * opacity),
          ],
        );
    }
  }
}
