/**
 * Script pour créer des comptes administrateurs prédéfinis
 *
 * Ce script doit être exécuté manuellement par les administrateurs de l'application
 * pour créer leurs comptes avec des privilèges d'administration.
 *
 * Usage:
 * 1. Modifier les informations des administrateurs ci-dessous
 * 2. Exécuter le script avec Node.js:
 *    node src/scripts/create-admin.js
 */

// Charger les variables d'environnement
require('dotenv').config({ path: require('path').resolve(__dirname, '../../.env') });

const mongoose = require('mongoose');
const Admin = require('../models/Admin');

// Configuration de la connexion à MongoDB
const connectDB = async () => {
  try {
    const uri = process.env.MONGODB_URI || 'mongodb+srv://dorsafayed23:<EMAIL>/seatrace?retryWrites=true&w=majority'; //'mongodb://localhost:27017/seatrace';
    console.log(`Tentative de connexion à MongoDB: ${uri}`);

    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connexion à MongoDB établie');
  } catch (error) {
    console.error('Erreur de connexion à MongoDB:', error.message);
    process.exit(1);
  }
};

// Liste des administrateurs à créer
const admins = [
  {
    email: '<EMAIL>',
    password: 'Admin123*',
    nom: 'Admin',
    prenom: 'SeaTrace',
    telephone: '12345678',
    roles: 'ROLE_ADMIN',
    isValidated: true,
    isBlocked: false
  },
  {
    email: '<EMAIL>',
    password: 'SafaAdmin1*',
    nom: 'Sfaxi',
    prenom: 'Safa',
    telephone: '29616410',
    roles: 'ROLE_ADMIN',
    isValidated: true,
    isBlocked: false
  },
  {
    email: '<EMAIL>',
    password: 'NawresAdmin2*',
    nom: 'Othmen',
    prenom: 'Nawres',
    telephone: '92413266',
    roles: 'ROLE_ADMIN',
    isValidated: true,
    isBlocked: false
  }
];

// Fonction pour créer les administrateurs
const createAdmins = async () => {
  try {
    // Connexion à la base de données
    await connectDB();

    console.log('🔧 Création des comptes administrateurs...\n');

    // Créer chaque administrateur
    for (const adminData of admins) {
      // Vérifier si l'administrateur existe déjà
      const existingAdmin = await Admin.findOne({ email: adminData.email });

      if (existingAdmin) {
        console.log(`⚠️  L'administrateur avec l'email ${adminData.email} existe déjà.`);
        continue;
      }

      // Créer le nouvel administrateur
      // Ne pas hacher le mot de passe ici, le middleware du modèle Admin le fera automatiquement
      const admin = new Admin(adminData);

      // Sauvegarder l'administrateur dans la base de données
      await admin.save();

      console.log(`✅ Administrateur créé avec succès: ${adminData.email}`);
      console.log(`   Nom: ${adminData.prenom} ${adminData.nom}`);
      console.log(`   Téléphone: ${adminData.telephone}\n`);
    }

    console.log('🎉 Création des administrateurs terminée avec succès !');
    console.log('\n📋 Comptes administrateurs disponibles :');
    
    // Afficher tous les administrateurs
    const allAdmins = await Admin.find({}, 'email nom prenom isValidated');
    allAdmins.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.email} - ${admin.prenom} ${admin.nom} ${admin.isValidated ? '✅' : '❌'}`);
    });

    // Fermer la connexion à la base de données
    await mongoose.connection.close();
    console.log('\n🔌 Connexion à MongoDB fermée');

    process.exit(0);
  } catch (error) {
    console.error('❌ Erreur lors de la création des administrateurs:', error.message);
    process.exit(1);
  }
};

// Exécuter la fonction principale
createAdmins();
