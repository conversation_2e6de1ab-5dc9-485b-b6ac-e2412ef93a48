/**
 * Routes pour la gestion des vétérinaires
 * Version simplifiée sans référence au modèle Prise
 */
const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Veterinaire = require('../models/Veterinaire');
const Lot = require('../models/Lot');
const { auth, checkRole } = require('../middleware/auth');
const { NotFoundError, BadRequestError, ForbiddenError } = require('../middleware/errorHandler');
const notificationService = require('../services/notificationService');

/**
 * @route GET /api/veterinaires
 * @desc Récupérer tous les vétérinaires
 * @access Public
 */
router.get('/', async (req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Requête GET /api/veterinaires avec paramètres:`, req.query);

    // Par défaut, récupérer tous les vétérinaires validés
    let filter = { isValidated: true };

    // Filtrer par statut de validation si spécifié explicitement
    if (req.query.isValidated !== undefined) {
      // Convertir explicitement la chaîne 'true' en booléen true
      const isValidatedValue = req.query.isValidated === 'true';
      filter.isValidated = isValidatedValue;
      console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Paramètre isValidated spécifié: ${req.query.isValidated}, filtre appliqué: ${filter.isValidated}`);
    } else {
      console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Paramètre isValidated non spécifié, utilisation de la valeur par défaut: ${filter.isValidated}`);
    }

    // Filtrer par statut de blocage si spécifié
    if (req.query.isBlocked !== undefined) {
      // Convertir explicitement la chaîne 'false' en booléen false
      const isBlockedValue = req.query.isBlocked === 'true';
      filter.isBlocked = isBlockedValue;
      console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Paramètre isBlocked spécifié: ${req.query.isBlocked}, filtre appliqué: ${filter.isBlocked}`);
    } else {
      // Par défaut, ne pas inclure les vétérinaires bloqués
      filter.isBlocked = false;
      console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Paramètre isBlocked non spécifié, utilisation de la valeur par défaut: ${filter.isBlocked}`);
    }

    // Si le paramètre all=true est fourni, récupérer tous les vétérinaires
    if (req.query.all === 'true') {
      // Supprimer les filtres de validation et de blocage
      filter = {};
    }

    console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Récupération des vétérinaires avec filtre:`, filter);

    // Récupérer tous les vétérinaires correspondant au filtre
    const veterinaires = await Veterinaire.find(filter);

    // Ajouter des logs pour le débogage
    console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] ${veterinaires.length} vétérinaires trouvés avec le filtre appliqué`);

    if (veterinaires.length > 0) {
      // Afficher les détails de tous les vétérinaires trouvés
      veterinaires.forEach((v, index) => {
        console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Vétérinaire #${index}:`, {
          id: v.id || v._id.toString(),
          nom: v.nom,
          prenom: v.prenom,
          email: v.email,
          isValidated: v.isValidated,
          isBlocked: v.isBlocked
        });
      });
    } else {
      // Vérifier s'il y a des vétérinaires dans la base de données
      const totalVeterinaires = await Veterinaire.countDocuments({});
      console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Nombre total de vétérinaires dans la base: ${totalVeterinaires}`);

      // Si aucun vétérinaire n'est trouvé avec le filtre mais qu'il y en a dans la base, afficher tous les vétérinaires pour le débogage
      if (totalVeterinaires > 0) {
        const allVeterinaires = await Veterinaire.find({});
        console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Tous les vétérinaires dans la base:`);

        allVeterinaires.forEach((v, index) => {
          console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Vétérinaire #${index}:`, {
            id: v.id || v._id.toString(),
            nom: v.nom,
            prenom: v.prenom,
            email: v.email,
            isValidated: v.isValidated,
            isBlocked: v.isBlocked
          });
        });

        // Vérifier s'il y a des vétérinaires validés
        const validatedVets = allVeterinaires.filter(v => v.isValidated === true);
        console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Nombre de vétérinaires validés: ${validatedVets.length}`);

        // Vérifier s'il y a des vétérinaires non bloqués
        const nonBlockedVets = allVeterinaires.filter(v => v.isBlocked !== true);
        console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Nombre de vétérinaires non bloqués: ${nonBlockedVets.length}`);

        // Vérifier s'il y a des vétérinaires validés et non bloqués
        const validAndNonBlockedVets = allVeterinaires.filter(v => v.isValidated === true && v.isBlocked !== true);
        console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Nombre de vétérinaires validés et non bloqués: ${validAndNonBlockedVets.length}`);
      }
    }

    // Transformer les données pour s'assurer que les champs id et isValidated sont présents
    const transformedVeterinaires = veterinaires.map(veterinaire => {
      const veterinaireObj = veterinaire.toJSON();
      // S'assurer que l'ID est correctement défini
      if (!veterinaireObj.id && veterinaireObj._id) {
        veterinaireObj.id = veterinaireObj._id.toString();
      }
      return veterinaireObj;
    });

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: transformedVeterinaires }, 'Liste des vétérinaires récupérée avec succès');
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ERROR [VETERINAIRE] Erreur lors de la récupération des vétérinaires:`, error);
    next(error);
  }
});

/**
 * @route GET /api/veterinaires/:id
 * @desc Récupérer un vétérinaire spécifique
 * @access Public
 */
router.get('/:id', async (req, res, next) => {
  try {
    let veterinaire;

    // Essayer de trouver par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      veterinaire = await Veterinaire.findById(req.params.id);
    }

    // Si non trouvé, essayer de trouver par ID personnalisé
    if (!veterinaire) {
      veterinaire = await Veterinaire.findOne({ id: req.params.id });
    }

    // Si non trouvé, essayer de trouver par email
    if (!veterinaire) {
      veterinaire = await Veterinaire.findOne({ email: req.params.id });
    }

    if (!veterinaire) {
      throw new NotFoundError('Vétérinaire non trouvé');
    }

    // Créer un objet de réponse avec des valeurs par défaut pour les champs null
    const veterinaireResponse = {
      id: veterinaire.id || veterinaire._id.toString(),
      email: veterinaire.email || '',
      roles: veterinaire.roles || 'ROLE_VETERINAIRE',
      nom: veterinaire.nom || '',
      prenom: veterinaire.prenom || '',
      telephone: veterinaire.telephone || '',
      photo: veterinaire.photo || '',
      cin: veterinaire.cin || '',
      specialite: veterinaire.specialite || '',
      licence: veterinaire.licence || '',
      matricule: veterinaire.matricule || '',
      etablissement: veterinaire.etablissement || '',
      adresse: veterinaire.adresse || '',
      isValidated: veterinaire.isValidated || false,
      isBlocked: veterinaire.isBlocked || false
    };

    // Journaliser les informations renvoyées
    console.log(`[${new Date().toISOString()}] INFO [VETERINAIRE] Détails vétérinaire récupérés: ${veterinaireResponse.prenom} ${veterinaireResponse.nom} (ID: ${veterinaireResponse.id})`);

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: veterinaireResponse }, 'Vétérinaire récupéré avec succès');
  } catch (error) {
    console.log(`[${new Date().toISOString()}] ERROR [VETERINAIRE] Erreur lors de la récupération du vétérinaire: ${error.message}`);
    next(error);
  }
});

/**
 * @route POST /api/veterinaires
 * @desc Créer un nouveau vétérinaire
 * @access Private (Admin uniquement)
 */
router.post('/', auth, checkRole('ROLE_ADMIN'), async (req, res, next) => {
  try {
    // Vérifier si l'email existe déjà
    const existingVeterinaire = await Veterinaire.findOne({ email: req.body.email });
    if (existingVeterinaire) {
      throw new BadRequestError('Un vétérinaire avec cet email existe déjà');
    }

    // Générer un identifiant unique si non fourni
    if (!req.body.id) {
      req.body.id = `VET-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }

    const veterinaire = new Veterinaire(req.body);
    const newVeterinaire = await veterinaire.save();

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.created({ data: newVeterinaire }, 'Vétérinaire créé avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PATCH /api/veterinaires/:id
 * @desc Mettre à jour un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.patch('/:id', auth, async (req, res, next) => {
  try {
    // Vérifier si l'utilisateur est autorisé à modifier ce vétérinaire
    const isAdmin = req.user.roles.includes('ROLE_ADMIN');
    const isSelf = req.user._id.toString() === req.params.id ||
                  (req.user.id && req.user.id === req.params.id);

    if (!isAdmin && !isSelf) {
      throw new ForbiddenError('Non autorisé à modifier ce vétérinaire');
    }

    // Empêcher la modification du rôle par un non-admin
    if (!isAdmin && req.body.roles) {
      delete req.body.roles;
    }

    let veterinaire;

    // Essayer de mettre à jour par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      veterinaire = await Veterinaire.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
      });
    }

    // Si non trouvé, essayer de mettre à jour par ID personnalisé
    if (!veterinaire) {
      veterinaire = await Veterinaire.findOneAndUpdate({ id: req.params.id }, req.body, {
        new: true,
        runValidators: true
      });
    }

    if (!veterinaire) {
      throw new NotFoundError('Vétérinaire non trouvé');
    }

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: veterinaire }, 'Vétérinaire mis à jour avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/veterinaires/:id
 * @desc Supprimer un vétérinaire
 * @access Private (Admin uniquement)
 */
router.delete('/:id', auth, checkRole('ROLE_ADMIN'), async (req, res, next) => {
  try {
    let veterinaire;

    // Essayer de supprimer par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      veterinaire = await Veterinaire.findByIdAndDelete(req.params.id);
    }

    // Si non trouvé, essayer de supprimer par ID personnalisé
    if (!veterinaire) {
      veterinaire = await Veterinaire.findOneAndDelete({ id: req.params.id });
    }

    if (!veterinaire) {
      throw new NotFoundError('Vétérinaire non trouvé');
    }

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: null }, 'Vétérinaire supprimé avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/veterinaires/:id/assign-maryeur
 * @desc Assigner un mareyeur à un lot
 * @access Private (Vétérinaire ou Admin)
 */
router.put('/:id/assign-maryeur', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Vérifier que les données nécessaires sont présentes
    if (!req.body.lotId || !req.body.maryeurId) {
      throw new BadRequestError('ID du lot et ID du mareyeur sont requis');
    }

    // Vérifier que le lot existe
    let lot;
    if (mongoose.Types.ObjectId.isValid(req.body.lotId)) {
      lot = await Lot.findById(req.body.lotId);
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Vérifier que le vétérinaire est bien celui associé au lot
    if (lot.veterinaire && lot.veterinaire.toString() !== req.user._id.toString()) {
      throw new ForbiddenError('Vous n\'êtes pas le vétérinaire associé à ce lot');
    }

    // Mettre à jour le lot avec le nouveau mareyeur
    lot.maryeur = req.body.maryeurId;
    await lot.save();

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('user', 'nom prenom bateau matricule')
      .populate('maryeur', 'nom prenom matricule')
      .populate('veterinaire', 'nom prenom matricule');

    // Notifier le mareyeur qu'un lot lui a été assigné
    try {
      await notificationService.notifierMaryeur(
        req.body.maryeurId,
        'Nouveau lot assigné',
        `Un lot vous a été assigné par le vétérinaire ${req.user.prenom} ${req.user.nom}`,
        'info',
        {
          reference: lot._id,
          referenceModel: 'Lot',
          urlAction: `/maryeur/lots/${lot._id}`
        }
      );
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la notification au mareyeur:', error);
    }

    // Notifier le pêcheur que son lot a été assigné à un nouveau mareyeur
    try {
      await notificationService.notifierPecheur(
        lot.user,
        'Lot assigné à un mareyeur',
        `Votre lot a été validé par le vétérinaire et assigné au mareyeur ${populatedLot.maryeur.prenom} ${populatedLot.maryeur.nom}`,
        'info',
        {
          reference: lot._id,
          referenceModel: 'Lot',
          urlAction: `/pecheur/lots/${lot._id}`
        }
      );
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la notification au pêcheur:', error);
    }

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: populatedLot }, 'Mareyeur assigné au lot avec succès');
  } catch (error) {
    next(error);
  }
});

module.exports = router;
