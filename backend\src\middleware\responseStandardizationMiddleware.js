/**
 * Middleware pour standardiser les réponses API
 * Ce middleware assure que toutes les réponses API suivent le même format
 * et gère les traductions selon la langue de la requête
 */

const TranslationService = require('../services/translationService');

/**
 * Middleware pour standardiser les réponses API
 * @param {Object} req - Objet requête Express
 * @param {Object} res - Objet réponse Express
 * @param {Function} next - Fonction next Express
 */
function standardizeApiResponses(req, res, next) {
  // Sauvegarder les méthodes originales
  const originalJson = res.json;
  const originalSend = res.send;
  const originalStatus = res.status;

  // Remplacer res.status pour capturer le code de statut
  res.status = function(code) {
    res.statusCode = code;
    return originalStatus.call(this, code);
  };

  // Remplacer res.json
  res.json = function(data) {
    // Si la réponse est déjà formatée (contient success), vérifier si elle est complète
    if (data && (data.success === true || data.success === false)) {
      // Vérifier si la réponse est complète
      if (!data.message && !data.error) {
        // Ajouter un message par défaut
        if (data.success) {
          data.message = getDefaultSuccessMessage(res.statusCode);
        } else {
          data.error = getDefaultErrorMessage(res.statusCode);
        }
      }

      return originalJson.call(this, data);
    }

    // Formater la réponse
    const formattedData = {
      success: isSuccessStatusCode(res.statusCode),
      data: data
    };

    // Ajouter un message approprié selon le code de statut
    if (isSuccessStatusCode(res.statusCode)) {
      formattedData.message = getDefaultSuccessMessage(res.statusCode);
    } else {
      formattedData.error = getDefaultErrorMessage(res.statusCode);
      delete formattedData.data;
    }

    return originalJson.call(this, formattedData);
  };

  // Remplacer res.send pour les réponses non-JSON
  res.send = function(data) {
    // Si c'est une chaîne ou un buffer, ne pas formater
    if (typeof data === 'string' || Buffer.isBuffer(data)) {
      return originalSend.call(this, data);
    }

    // Sinon, utiliser json pour formater
    return res.json(data);
  };

  // Ajouter des méthodes utilitaires avec support multilingue
  res.success = function(data, message, statusCode = 200, translationKey = null) {
    const lang = TranslationService.getLanguageFromRequest(req);
    let finalMessage = message;

    if (translationKey) {
      finalMessage = TranslationService.translate(translationKey, lang);
    } else if (!message) {
      finalMessage = getDefaultSuccessMessage(statusCode, lang);
    }

    return res.status(statusCode).json({
      success: true,
      message: finalMessage,
      data: data
    });
  };

  res.created = function(data, message = null, translationKey = 'success.resourceCreated') {
    const lang = TranslationService.getLanguageFromRequest(req);
    const finalMessage = message || TranslationService.translate(translationKey, lang);

    return res.status(201).json({
      success: true,
      message: finalMessage,
      data: data
    });
  };

  res.error = function(message, statusCode = 400, details = null, translationKey = null) {
    const lang = TranslationService.getLanguageFromRequest(req);
    let finalMessage = message;

    if (translationKey) {
      finalMessage = TranslationService.translate(translationKey, lang);
    } else if (!message) {
      finalMessage = getDefaultErrorMessage(statusCode, lang);
    }

    const response = {
      success: false,
      error: finalMessage
    };

    if (details) {
      response.details = details;
    }

    return res.status(statusCode).json(response);
  };

  next();
}

/**
 * Vérifie si un code de statut HTTP est un code de succès
 * @param {number} statusCode - Code de statut HTTP
 * @returns {boolean} - true si c'est un code de succès, false sinon
 */
function isSuccessStatusCode(statusCode) {
  return statusCode >= 200 && statusCode < 300;
}

/**
 * Retourne un message de succès par défaut en fonction du code de statut
 * @param {number} statusCode - Code de statut HTTP
 * @param {string} lang - Code de langue
 * @returns {string} - Message de succès par défaut
 */
function getDefaultSuccessMessage(statusCode, lang = 'fr') {
  switch (statusCode) {
    case 200:
      return TranslationService.translate('success.operationSuccess', lang);
    case 201:
      return TranslationService.translate('success.resourceCreated', lang);
    case 202:
      return TranslationService.translate('success.requestAccepted', lang);
    case 204:
      return TranslationService.translate('success.operationSuccessNoContent', lang);
    default:
      return TranslationService.translate('success.operationSuccess', lang);
  }
}

/**
 * Retourne un message d'erreur par défaut en fonction du code de statut
 * @param {number} statusCode - Code de statut HTTP
 * @param {string} lang - Code de langue
 * @returns {string} - Message d'erreur par défaut
 */
function getDefaultErrorMessage(statusCode, lang = 'fr') {
  switch (statusCode) {
    case 400:
      return TranslationService.translate('error.invalidRequest', lang);
    case 401:
      return TranslationService.translate('error.authenticationRequired', lang);
    case 403:
      return TranslationService.translate('error.accessDenied', lang);
    case 404:
      return TranslationService.translate('error.resourceNotFound', lang);
    case 409:
      return TranslationService.translate('error.conflict', lang);
    case 422:
      return TranslationService.translate('error.unprocessableEntity', lang);
    case 500:
      return TranslationService.translate('error.internalServerError', lang);
    case 503:
      return TranslationService.translate('error.serviceUnavailable', lang);
    default:
      return TranslationService.translate('error.generalError', lang);
  }
}

module.exports = standardizeApiResponses;
