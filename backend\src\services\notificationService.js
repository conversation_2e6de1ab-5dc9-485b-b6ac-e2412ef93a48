/**
 * Service de gestion des notifications
 * Fournit des méthodes pour créer et gérer les notifications
 * Version standardisée pour être cohérente avec le frontend
 */
const Notification = require('../models/Notification');

// Importer le service WebSocket
const websocketService = require('./websocketService');

/**
 * Crée une notification pour un utilisateur spécifique
 * Méthode générique standardisée pour être cohérente avec le frontend
 *
 * @param {Object} params - Paramètres de la notification
 * @param {string} params.destinataireId - ID du destinataire
 * @param {string} params.destinataireType - Type du destinataire (Pecheur, Veterinaire, Maryeur, Client, Admin)
 * @param {string} params.titre - Titre de la notification
 * @param {string} params.contenu - Contenu de la notification
 * @param {string} params.type - Type de notification (info, success, warning, error)
 * @param {string} [params.referenceId] - ID de l'objet réf<PERSON>rencé (optionnel)
 * @param {string} [params.referenceType] - Type de l'objet référencé (optionnel)
 * @param {string} [params.urlAction] - URL d'action (optionnel)
 * @returns {Promise<Object>} La notification créée
 */
const createNotification = async (params) => {
  const {
    destinataireId,
    destinataireType,
    titre,
    contenu,
    type = 'info',
    referenceId,
    referenceType,
    urlAction
  } = params;

  // Validation des paramètres
  if (!destinataireId || !destinataireType || !titre || !contenu) {
    throw new Error('Paramètres manquants pour la création de notification');
  }

  // Vérifier que le type de destinataire est valide
  const typesValides = ['Pecheur', 'Veterinaire', 'Maryeur', 'Client', 'Admin'];
  if (!typesValides.includes(destinataireType)) {
    throw new Error(`Type de destinataire non pris en charge: ${destinataireType}`);
  }

  // Créer la notification
  const notification = new Notification({
    destinataire: destinataireId,
    destinataireModel: destinataireType,
    titre,
    contenu,
    type,
    reference: referenceId,
    referenceModel: referenceType,
    urlAction
  });

  // Sauvegarder la notification dans la base de données
  const savedNotification = await notification.save();

  // Envoyer la notification en temps réel via WebSocket
  websocketService.sendNotification(destinataireId, savedNotification);

  return savedNotification;
};

/**
 * Crée une notification pour un pêcheur
 * @param {string} pecheurId - ID du pêcheur
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Object>} La notification créée
 */
const notifierPecheur = async (pecheurId, titre, contenu, type = 'info', options = {}) => {
  return await createNotification({
    destinataireId: pecheurId,
    destinataireType: 'Pecheur',
    titre,
    contenu,
    type,
    referenceId: options.reference,
    referenceType: options.referenceModel,
    urlAction: options.urlAction
  });
};

/**
 * Crée une notification pour un vétérinaire
 * @param {string} veterinaireId - ID du vétérinaire
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Object>} La notification créée
 */
const notifierVeterinaire = async (veterinaireId, titre, contenu, type = 'info', options = {}) => {
  return await createNotification({
    destinataireId: veterinaireId,
    destinataireType: 'Veterinaire',
    titre,
    contenu,
    type,
    referenceId: options.reference,
    referenceType: options.referenceModel,
    urlAction: options.urlAction
  });
};

/**
 * Crée une notification pour un mareyeur
 * @param {string} maryeurId - ID du mareyeur
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Object>} La notification créée
 */
const notifierMaryeur = async (maryeurId, titre, contenu, type = 'info', options = {}) => {
  return await createNotification({
    destinataireId: maryeurId,
    destinataireType: 'Maryeur',
    titre,
    contenu,
    type,
    referenceId: options.reference,
    referenceType: options.referenceModel,
    urlAction: options.urlAction
  });
};

/**
 * Crée une notification pour un client
 * @param {string} clientId - ID du client
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Object>} La notification créée
 */
const notifierClient = async (clientId, titre, contenu, type = 'info', options = {}) => {
  return await createNotification({
    destinataireId: clientId,
    destinataireType: 'Client',
    titre,
    contenu,
    type,
    referenceId: options.reference,
    referenceType: options.referenceModel,
    urlAction: options.urlAction
  });
};

/**
 * Notifie tous les vétérinaires
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Array>} Les notifications créées
 */
const notifierTousVeterinaires = async (titre, contenu, type = 'info', options = {}) => {
  const Veterinaire = require('../models/Veterinaire');
  const veterinaires = await Veterinaire.find();

  const notifications = [];
  for (const veterinaire of veterinaires) {
    const notification = await notifierVeterinaire(
      veterinaire._id,
      titre,
      contenu,
      type,
      options
    );
    notifications.push(notification);
  }

  // Envoyer une notification à tous les vétérinaires via WebSocket
  websocketService.sendNotificationByUserType('Veterinaire', {
    titre,
    contenu,
    type,
    ...options
  });

  return notifications;
};

/**
 * Crée une notification pour un administrateur
 * @param {string} adminId - ID de l'administrateur
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Object>} La notification créée
 */
const notifierAdmin = async (adminId, titre, contenu, type = 'info', options = {}) => {
  return await createNotification({
    destinataireId: adminId,
    destinataireType: 'Admin',
    titre,
    contenu,
    type,
    referenceId: options.reference,
    referenceType: options.referenceModel,
    urlAction: options.urlAction
  });
};

/**
 * Notifie tous les administrateurs
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Array>} Les notifications créées
 */
const notifierTousAdmins = async (titre, contenu, type = 'info', options = {}) => {
  const Admin = require('../models/Admin');
  const admins = await Admin.find();

  const notifications = [];
  for (const admin of admins) {
    const notification = await notifierAdmin(
      admin._id,
      titre,
      contenu,
      type,
      options
    );
    notifications.push(notification);
  }

  return notifications;
};

/**
 * Notifie tous les utilisateurs d'un type spécifique
 * @param {string} userType - Type d'utilisateur (Pecheur, Veterinaire, Maryeur, Client, Admin)
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Array>} Les notifications créées
 */
const notifierTousUtilisateurs = async (userType, titre, contenu, type = 'info', options = {}) => {
  let Model;
  let notifierFn;

  switch (userType) {
    case 'Pecheur':
      Model = require('../models/Pecheur');
      notifierFn = notifierPecheur;
      break;
    case 'Veterinaire':
      Model = require('../models/Veterinaire');
      notifierFn = notifierVeterinaire;
      break;
    case 'Maryeur':
      Model = require('../models/Maryeur');
      notifierFn = notifierMaryeur;
      break;
    case 'Client':
      Model = require('../models/Client');
      notifierFn = notifierClient;
      break;
    case 'Admin':
      Model = require('../models/Admin');
      notifierFn = notifierAdmin;
      break;
    default:
      throw new Error(`Type d'utilisateur non pris en charge: ${userType}`);
  }

  const users = await Model.find();

  const notifications = [];
  for (const user of users) {
    const notification = await notifierFn(
      user._id,
      titre,
      contenu,
      type,
      options
    );
    notifications.push(notification);
  }

  return notifications;
};

/**
 * Crée une notification pour un utilisateur spécifique
 * @param {string} userId - ID de l'utilisateur
 * @param {string} userType - Type d'utilisateur (Pecheur, Veterinaire, Maryeur, Client, Admin)
 * @param {string} titre - Titre de la notification
 * @param {string} contenu - Contenu de la notification
 * @param {string} type - Type de notification (info, success, warning, error)
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Object>} La notification créée
 */
const notifierUtilisateur = async (userId, userType, titre, contenu, type = 'info', options = {}) => {
  return await createNotification({
    destinataireId: userId,
    destinataireType: userType,
    titre,
    contenu,
    type,
    referenceId: options.reference,
    referenceType: options.referenceModel,
    urlAction: options.urlAction
  });
};

module.exports = {
  // Méthode standardisée pour être cohérente avec le frontend
  createNotification,

  // Méthodes spécifiques pour la compatibilité avec le code existant
  notifierPecheur,
  notifierVeterinaire,
  notifierMaryeur,
  notifierClient,
  notifierAdmin,
  notifierTousVeterinaires,
  notifierTousAdmins,
  notifierTousUtilisateurs,
  notifierUtilisateur
};
