{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\Android\\src\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\peche_app\\peche_app\\android\\app\\.cxx\\Debug\\4w5qe1f6\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\Android\\src\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\peche_app\\peche_app\\android\\app\\.cxx\\Debug\\4w5qe1f6\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}