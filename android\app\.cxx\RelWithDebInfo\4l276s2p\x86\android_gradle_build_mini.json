{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\peche_app\\android\\app\\.cxx\\RelWithDebInfo\\4l276s2p\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\peche_app\\android\\app\\.cxx\\RelWithDebInfo\\4l276s2p\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}