import 'package:flutter/material.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:intl/intl.dart';

class VeterinaireHistoryScreen extends StatefulWidget {
  const VeterinaireHistoryScreen({super.key});

  @override
  State<VeterinaireHistoryScreen> createState() =>
      _VeterinaireHistoryScreenState();
}

class _VeterinaireHistoryScreenState extends State<VeterinaireHistoryScreen>
    with TickerProviderStateMixin {
  final UnifiedLotService _lotService = UnifiedLotService();
  final UnifiedAuthService _authService = UnifiedAuthService();

  List<Lot> _allLots = [];
  List<Lot> _filteredLots = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, approved, rejected
  String? _selectedSpecies;
  late TabController _tabController;
  List<String> _availableSpecies = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadHistory() async {
    try {
      setState(() => _isLoading = true);

      final user = await _authService.getCurrentUser();
      if (user == null) return;

      final lots = await _lotService.getVeterinaireHistory(user.id);

      setState(() {
        _allLots = lots;
        _extractAvailableSpecies();
        _applyFilter();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
      }
    }
  }

  void _extractAvailableSpecies() {
    final species = _allLots.map((lot) => lot.especeNom).toSet().toList();
    species.sort();
    _availableSpecies = species;
  }

  void _applyFilter() {
    List<Lot> filtered = _allLots;

    // Filtre par statut
    switch (_selectedFilter) {
      case 'approved':
        filtered =
            filtered
                .where((lot) => lot.test && lot.isValidated == true)
                .toList();
        break;
      case 'rejected':
        filtered =
            filtered
                .where((lot) => lot.test && lot.isValidated == false)
                .toList();
        break;
      default:
        // Tous les lots
        break;
    }

    // Filtre par espèce
    if (_selectedSpecies != null && _selectedSpecies!.isNotEmpty) {
      filtered =
          filtered
              .where(
                (lot) =>
                    lot.especeNom.toLowerCase() ==
                    _selectedSpecies!.toLowerCase(),
              )
              .toList();
    }

    _filteredLots = filtered;
  }

  int _getFilteredCount(String filter) {
    List<Lot> lots = _allLots;

    // Appliquer le filtre par espèce si sélectionné
    if (_selectedSpecies != null && _selectedSpecies!.isNotEmpty) {
      lots =
          lots
              .where(
                (lot) =>
                    lot.especeNom.toLowerCase() ==
                    _selectedSpecies!.toLowerCase(),
              )
              .toList();
    }

    // Appliquer le filtre par statut
    switch (filter) {
      case 'approved':
        return lots.where((lot) => lot.test && lot.isValidated == true).length;
      case 'rejected':
        return lots.where((lot) => lot.test && lot.isValidated == false).length;
      default:
        return lots.length;
    }
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = const Color(0xFF1565C0);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Historique des Validations'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          onTap: (index) {
            setState(() {
              switch (index) {
                case 0:
                  _selectedFilter = 'all';
                  break;
                case 1:
                  _selectedFilter = 'approved';
                  break;
                case 2:
                  _selectedFilter = 'rejected';
                  break;
              }
              _applyFilter();
            });
          },
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.list_alt, size: 18),
                  const SizedBox(width: 8),
                  Text('Tous (${_getFilteredCount('all')})'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check_circle, size: 18),
                  const SizedBox(width: 8),
                  Text('Approuvés (${_getFilteredCount('approved')})'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.cancel, size: 18),
                  const SizedBox(width: 8),
                  Text('Refusés (${_getFilteredCount('rejected')})'),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          if (_availableSpecies.isNotEmpty) _buildSpeciesFilter(),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : RefreshIndicator(
                      onRefresh: _loadHistory,
                      child:
                          _filteredLots.isEmpty
                              ? _buildEmptyState()
                              : ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: _filteredLots.length,
                                itemBuilder: (context, index) {
                                  return _buildLotCard(_filteredLots[index]);
                                },
                              ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpeciesFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.filter_list, color: Color(0xFF1565C0)),
          const SizedBox(width: 12),
          const Text(
            'Filtrer par espèce:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Color(0xFF1565C0),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedSpecies,
              decoration: InputDecoration(
                hintText: 'Toutes les espèces',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('Toutes les espèces'),
                ),
                ..._availableSpecies.map(
                  (species) => DropdownMenuItem<String>(
                    value: species,
                    child: Text(species),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSpecies = value;
                  _applyFilter();
                });
              },
            ),
          ),
          if (_selectedSpecies != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                setState(() {
                  _selectedSpecies = null;
                  _applyFilter();
                });
              },
              icon: const Icon(Icons.clear, color: Colors.grey),
              tooltip: 'Effacer le filtre',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucun lot dans l\'historique',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Les lots que vous validez apparaîtront ici',
            style: TextStyle(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildLotCard(Lot lot) {
    final theme = Theme.of(context);
    final primaryColor = const Color(0xFF1565C0);
    final isApproved = lot.test && lot.isValidated;
    final isRejected = lot.test && !lot.isValidated;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showLotDetails(lot),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isApproved
                              ? Colors.green.withValues(alpha: 0.1)
                              : isRejected
                              ? Colors.red.withValues(alpha: 0.1)
                              : Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isApproved
                              ? Icons.check_circle
                              : isRejected
                              ? Icons.cancel
                              : Icons.pending,
                          size: 16,
                          color:
                              isApproved
                                  ? Colors.green
                                  : isRejected
                                  ? Colors.red
                                  : Colors.orange,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          isApproved
                              ? 'Approuvé'
                              : isRejected
                              ? 'Refusé'
                              : 'En attente',
                          style: TextStyle(
                            color:
                                isApproved
                                    ? Colors.green
                                    : isRejected
                                    ? Colors.red
                                    : Colors.orange,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    lot.identifiant,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lot.especeNom,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: primaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${lot.poids} kg',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (lot.photo != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        lot.photo!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Validé le ${lot.dateTest != null ? DateFormat('dd/MM/yyyy à HH:mm').format(lot.dateTest!) : (lot.dateSoumission != null ? DateFormat('dd/MM/yyyy à HH:mm').format(lot.dateSoumission!) : 'Date inconnue')}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              if (lot.raisonRejet != null && lot.raisonRejet!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.red[700],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Raison du rejet: ${lot.raisonRejet}',
                          style: TextStyle(
                            color: Colors.red[700],
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showLotDetails(Lot lot) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails du Lot ${lot.identifiant}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow('Espèce', lot.especeNom),
                  _buildDetailRow('Poids', '${lot.poids} kg'),
                  _buildDetailRow('Température', '${lot.temperature}°C'),
                  _buildDetailRow('Lieu', lot.lieu ?? 'Non spécifié'),
                  _buildDetailRow(
                    'Date soumission',
                    lot.dateSoumission != null
                        ? DateFormat(
                          'dd/MM/yyyy à HH:mm',
                        ).format(lot.dateSoumission!)
                        : 'Non spécifiée',
                  ),
                  if (lot.dateTest != null)
                    _buildDetailRow(
                      'Date validation',
                      DateFormat('dd/MM/yyyy à HH:mm').format(lot.dateTest!),
                    ),
                  _buildDetailRow(
                    'Statut',
                    lot.test
                        ? (lot.isValidated ? 'Approuvé' : 'Refusé')
                        : 'En attente',
                  ),
                  if (lot.raisonRejet != null && lot.raisonRejet!.isNotEmpty)
                    _buildDetailRow('Raison du rejet', lot.raisonRejet!),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
