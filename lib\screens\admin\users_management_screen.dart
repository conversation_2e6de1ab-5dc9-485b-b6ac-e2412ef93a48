import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/utils/error_handler.dart';

import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';

class UsersManagementScreen extends StatefulWidget {
  const UsersManagementScreen({super.key});

  @override
  State<UsersManagementScreen> createState() => _UsersManagementScreenState();
}

class _UsersManagementScreenState extends State<UsersManagementScreen>
    with SingleTickerProviderStateMixin {
  final _responsiveService = ResponsiveService();

  late TabController _tabController;
  bool _isLoading = true;
  String? _errorMessage;

  List<Map<String, dynamic>> _pecheurs = [];
  List<Map<String, dynamic>> _veterinaires = [];
  List<Map<String, dynamic>> _maryeurs = [];
  List<Map<String, dynamic>> _clients = [];

  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadAllUsers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAllUsers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Charger les pêcheurs
      final pecheursResponse = await UnifiedApiService().get(
        'pecheurs?all=true',
      );
      final pecheursData = pecheursResponse['data'];
      final pecheurs = pecheursData is List ? pecheursData : <dynamic>[];

      // Charger les vétérinaires
      final veterinairesResponse = await UnifiedApiService().get(
        'veterinaires?all=true',
      );
      final veterinairesData = veterinairesResponse['data'];
      final veterinaires =
          veterinairesData is List ? veterinairesData : <dynamic>[];

      // Charger les mareyeurs
      final maryeursResponse = await UnifiedApiService().get(
        'maryeurs?all=true',
      );
      final maryeursData = maryeursResponse['data'];
      final maryeurs = maryeursData is List ? maryeursData : <dynamic>[];

      // Charger les clients
      final clientsResponse = await UnifiedApiService().get('clients?all=true');
      final clientsData = clientsResponse['data'];
      final clients = clientsData is List ? clientsData : <dynamic>[];

      setState(() {
        _pecheurs = _mapUsers(pecheurs, 'Pêcheur');
        _veterinaires = _mapUsers(veterinaires, 'Vétérinaire');
        _maryeurs = _mapUsers(maryeurs, 'Mareyeur');
        _clients = _mapUsers(clients, 'Client');
        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UsersManagementScreen._loadAllUsers',
      );
      setState(() {
        _errorMessage =
            'Erreur lors du chargement des utilisateurs: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _mapUsers(List<dynamic> users, String userType) {
    return users.map((user) {
      return {
        'id': user['id'] ?? user['_id'],
        'nom': user['nom'] ?? '',
        'prenom': user['prenom'] ?? '',
        'email': user['email'] ?? '',
        'telephone': user['telephone'] ?? '',
        'type': userType,
        'photo': user['photo'],
        'isValidated': user['isValidated'] ?? false,
        'isBlocked': user['isBlocked'] ?? false,
        'createdAt':
            user['createdAt'] != null
                ? DateTime.parse(user['createdAt'])
                : DateTime.now(),
      };
    }).toList();
  }

  Future<void> _toggleUserBlock(String userId) async {
    try {
      await UnifiedApiService().post('admins/toggle-block/$userId', {});

      // Mettre à jour la liste des utilisateurs
      await _loadAllUsers();

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Statut de l\'utilisateur modifié avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UsersManagementScreen._toggleUserBlock',
      );
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de la modification: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showUserDetails(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Détails de ${user['prenom']} ${user['nom']}'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('Type', user['type']),
                _buildDetailRow('Email', user['email']),
                _buildDetailRow(
                  'Téléphone',
                  user['telephone'] ?? 'Non renseigné',
                ),
                _buildDetailRow(
                  'Statut',
                  user['isValidated'] ? 'Validé' : 'En attente',
                ),
                _buildDetailRow('Bloqué', user['isBlocked'] ? 'Oui' : 'Non'),
                _buildDetailRow(
                  'Date de création',
                  user['createdAt'] != null
                      ? (user['createdAt'] as DateTime).toString().split(' ')[0]
                      : 'Non disponible',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Fermer'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _toggleUserBlock(user['id']);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: user['isBlocked'] ? Colors.green : Colors.red,
              ),
              child: Text(user['isBlocked'] ? 'Débloquer' : 'Bloquer'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('Erreur', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Une erreur inconnue est survenue',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAllUsers,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView(String userType) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.person_off, color: Colors.grey, size: 60),
            const SizedBox(height: 16),
            Text(
              'Aucun $userType trouvé',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Il n\'y a pas de $userType enregistré dans le système',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _filterUsers(List<Map<String, dynamic>> users) {
    if (_searchQuery.isEmpty) {
      return users;
    }

    final query = _searchQuery.toLowerCase();
    return users.where((user) {
      final nom = user['nom'].toString().toLowerCase();
      final prenom = user['prenom'].toString().toLowerCase();
      final email = user['email'].toString().toLowerCase();
      final telephone = user['telephone'].toString().toLowerCase();

      return nom.contains(query) ||
          prenom.contains(query) ||
          email.contains(query) ||
          telephone.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des utilisateurs'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Pêcheurs'),
            Tab(text: 'Vétérinaires'),
            Tab(text: 'Mareyeurs'),
            Tab(text: 'Clients'),
          ],
          labelColor: theme.primaryColor,
          unselectedLabelColor: theme.textTheme.bodyMedium?.color,
          indicatorColor: theme.primaryColor,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Barre de recherche
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SeaTextField(
                hint: 'Rechercher un utilisateur...',
                prefixIcon: Icons.search,
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),

            // Contenu principal
            Expanded(
              child:
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _errorMessage != null
                      ? _buildErrorView()
                      : TabBarView(
                        controller: _tabController,
                        children: [
                          // Onglet Pêcheurs
                          _buildUserList(_filterUsers(_pecheurs), 'Pêcheur'),

                          // Onglet Vétérinaires
                          _buildUserList(
                            _filterUsers(_veterinaires),
                            'Vétérinaire',
                          ),

                          // Onglet Mareyeurs
                          _buildUserList(_filterUsers(_maryeurs), 'Mareyeur'),

                          // Onglet Clients
                          _buildUserList(_filterUsers(_clients), 'Client'),
                        ],
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserList(List<Map<String, dynamic>> users, String userType) {
    if (users.isEmpty) {
      return _buildEmptyView(userType);
    }

    return RefreshIndicator(
      onRefresh: _loadAllUsers,
      child: ListView.builder(
        padding: _responsiveService.adaptivePadding(context),
        itemCount: users.length,
        itemBuilder: (context, index) {
          final user = users[index];
          final userName = '${user['prenom']} ${user['nom']}';
          final userEmail = user['email'];

          final userPhoto = user['photo'];
          final isValidated = user['isValidated'] as bool;
          final isBlocked = user['isBlocked'] as bool;

          return SeaListItem(
            title: userName,
            subtitle: userEmail,
            imageUrl: userPhoto,
            initials:
                userName.isNotEmpty
                    ? userName
                        .split(' ')
                        .map((e) => e.isNotEmpty ? e[0] : '')
                        .join()
                    : '?',
            badge:
                isBlocked
                    ? 'Bloqué'
                    : isValidated
                    ? 'Validé'
                    : 'En attente',
            badgeColor:
                isBlocked
                    ? Colors.red
                    : isValidated
                    ? Colors.green
                    : Colors.orange,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(
                    isBlocked ? Icons.lock_open : Icons.lock,
                    color: isBlocked ? Colors.orange : Colors.red,
                  ),
                  onPressed: () => _toggleUserBlock(user['id']),
                  tooltip: isBlocked ? 'Débloquer' : 'Bloquer',
                ),
                IconButton(
                  icon: const Icon(Icons.info_outline),
                  onPressed: () => _showUserDetails(user),
                  tooltip: 'Détails',
                ),
              ],
            ),
            onTap: () => _showUserDetails(user),
          );
        },
      ),
    );
  }
}
