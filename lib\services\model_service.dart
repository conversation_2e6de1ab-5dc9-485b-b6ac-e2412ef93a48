import 'package:seatrace/models/client.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/admin.dart';
import 'package:seatrace/models/base_user.dart';
import 'package:seatrace/utils/field_constants.dart';

/// Service pour la gestion des modèles
///
/// Ce service fournit des méthodes pour créer des modèles à partir de données JSON,
/// standardiser les données entre le frontend et le backend, et gérer les conversions
/// entre différents formats.
///
/// Il implémente le pattern Singleton pour garantir une instance unique dans toute l'application.
class ModelService {
  // Instance unique du service (Singleton)
  static final ModelService _instance = ModelService._internal();

  // Factory constructor qui retourne l'instance unique
  factory ModelService() => _instance;

  // Constructeur privé pour le Singleton
  ModelService._internal();

  /// Crée un utilisateur à partir d'une map, en déterminant automatiquement le type
  ///
  /// Cette méthode analyse les rôles de l'utilisateur pour déterminer son type
  /// (Pecheur, Veterinaire, Maryeur, Client ou Admin) et crée l'objet correspondant.
  ///
  /// @param map La map contenant les données de l'utilisateur
  /// @return Un objet utilisateur du type approprié
  dynamic createUserFromMap(Map<String, dynamic> map) {
    // Standardiser la map avant de créer l'utilisateur
    final standardizedMap = standardizeMap(map);

    // Extraire les rôles (chaîne vide par défaut si non défini)
    final String roles = standardizedMap['roles'] ?? '';

    // Créer l'objet utilisateur en fonction des rôles
    if (BaseUser.isPecheur(roles)) {
      return Pecheur.fromMap(standardizedMap);
    } else if (BaseUser.isVeterinaire(roles)) {
      return Veterinaire.fromMap(standardizedMap);
    } else if (BaseUser.isMaryeur(roles)) {
      return Maryeur.fromMap(standardizedMap);
    } else if (BaseUser.isAdmin(roles)) {
      // Utiliser le modèle Admin pour les administrateurs
      return Admin.fromJson(standardizedMap);
    } else {
      // Par défaut, on considère que c'est un client
      return Client.fromMap(standardizedMap);
    }
  }

  /// Convertit un utilisateur en map
  Map<String, dynamic> userToMap(dynamic user) {
    if (user is Pecheur) {
      return user.toMap();
    } else if (user is Veterinaire) {
      return user.toMap();
    } else if (user is Maryeur) {
      return user.toMap();
    } else if (user is Admin) {
      return user.toMap();
    } else if (user is Client) {
      return user.toMap();
    } else {
      throw ArgumentError('Type d\'utilisateur non pris en charge');
    }
  }

  /// Standardise l'ID dans une map
  Map<String, dynamic> standardizeId(Map<String, dynamic> map) {
    // Extraire l'ID en utilisant la méthode de BaseUser
    final id = BaseUser.extractId(map);

    // Si un ID a été trouvé, le standardiser
    if (id != null) {
      // Vérifier si l'ID est au format ObjectId
      String cleanId = id;
      if (id.toString().contains('ObjectId')) {
        final regex = RegExp(r"ObjectId\('([^']+)'\)");
        final match = regex.firstMatch(id.toString());
        if (match != null && match.groupCount >= 1) {
          cleanId = match.group(1)!;
        }
      }

      // Ne pas supprimer _id pour maintenir la compatibilité avec certaines parties du code
      // mais ajouter une version standardisée
      map['id'] = cleanId;

      // Si _id est un objet avec $oid, le standardiser aussi
      if (map.containsKey('_id') &&
          map['_id'] is Map &&
          map['_id'].containsKey('\$oid')) {
        map['_id'] = map['_id']['\$oid'];
      }
    }

    return map;
  }

  /// Standardise les rôles dans une map
  Map<String, dynamic> standardizeRoles(Map<String, dynamic> map) {
    final roles = map['roles'];
    if (roles is List) {
      map['roles'] = roles.join(',');
    }
    return map;
  }

  /// Standardise les champs booléens dans une map
  Map<String, dynamic> standardizeBooleans(Map<String, dynamic> map) {
    // Liste des champs booléens connus
    final booleanFields = [
      'isValidated',
      'isValid',
      'isBlocked',
      'lue',
      'status',
      'vendu',
      'test',
      'isProduit',
      'isAuctionActive',
    ];

    for (final field in booleanFields) {
      if (map.containsKey(field)) {
        final value = map[field];
        if (value is! bool) {
          if (value is int) {
            map[field] = value == 1;
          } else if (value is String) {
            map[field] = value.toLowerCase() == 'true' || value == '1';
          }
        }
      }
    }

    return map;
  }

  /// Standardise les noms de champs dans une map
  ///
  /// Cette méthode utilise les constantes définies dans FieldConstants
  /// pour standardiser les noms de champs et assurer la cohérence
  /// entre le frontend et le backend.
  ///
  /// @param map La map à standardiser
  /// @return Une map avec des noms de champs standardisés
  Map<String, dynamic> standardizeFieldNames(Map<String, dynamic> map) {
    // Mappings de noms de champs non standards vers standards
    final fieldMappings = {
      // Champs booléens
      'isValid': FieldConstants.isValidated,
      'is_valid': FieldConstants.isValidated,
      'is_validated': FieldConstants.isValidated,
      'status': FieldConstants.isValidated,
      'isBlocked': FieldConstants.isBlocked,
      'is_blocked': FieldConstants.isBlocked,

      // Champs de lots
      'poid': FieldConstants.poids,
      'datetest': FieldConstants.dateTest,
      'date_test': FieldConstants.dateTest,
      'datesoumettre': FieldConstants.dateSoumission,
      'date_soumission': FieldConstants.dateSoumission,
      'prixinitial': FieldConstants.prixInitial,
      'prix_initial': FieldConstants.prixInitial,
      'prixminimal': FieldConstants.prixMinimal,
      'prix_minimal': FieldConstants.prixMinimal,
      'prixfinale': FieldConstants.prixFinal,
      'prix_final': FieldConstants.prixFinal,
      'vendre': FieldConstants.vendu,
      'is_vendu': FieldConstants.vendu,

      // Champs d'ID et références
      'prise_id': FieldConstants.priseId,
      'user_id': FieldConstants.userId,
      'pecheur_id': FieldConstants.pecheurId,
      'veterinaire_id': FieldConstants.veterinaireId,
      'maryeur_id': FieldConstants.maryeurId,
      'acheteur_id': FieldConstants.acheteurId,

      // Autres champs
      'is_produit': FieldConstants.isProduit,
      'vitirinaire': FieldConstants.veterinaire,
      'vitirinaire_id': FieldConstants.veterinaireId,
      'created_at': FieldConstants.createdAt,
      'updated_at': FieldConstants.updatedAt,
      'date_creation': FieldConstants.createdAt,
      'date_validation': FieldConstants.updatedAt,
      'date_fin_enchere': FieldConstants.dateFinEnchere,
      'is_auction_active': FieldConstants.isAuctionActive,
      'prix_depart': FieldConstants.prixInitial,
      'prix_actuel': FieldConstants.prixFinal,
      'image_url': FieldConstants.imageUrl,
      'nom_scientifique': FieldConstants.nomScientifique,
      'prix_moyen': FieldConstants.prixMoyen,
      'is_active': FieldConstants.isActive,
      'espece_nom': FieldConstants.especeNom,
      'espece_id': FieldConstants.especeId,
    };

    // Copier la map pour éviter de modifier la map originale pendant l'itération
    final mapCopy = Map<String, dynamic>.from(map);

    // Remplacer les noms de champs non standards par des noms standards
    for (final entry in fieldMappings.entries) {
      if (mapCopy.containsKey(entry.key)) {
        map[entry.value] = mapCopy[entry.key];
        // Ne pas supprimer l'original pour maintenir la compatibilité
      }
    }

    return map;
  }

  /// Standardise une map pour qu'elle soit compatible avec les modèles
  ///
  /// Cette méthode applique plusieurs transformations à une map pour standardiser
  /// ses données et les rendre compatibles avec les modèles du frontend.
  /// Elle standardise les IDs, les rôles, les booléens et les noms de champs.
  ///
  /// Pour optimiser les performances, elle crée une copie de la map originale
  /// et applique toutes les transformations sur cette copie, évitant ainsi
  /// de créer plusieurs copies intermédiaires.
  ///
  /// @param map La map à standardiser
  /// @return Une map standardisée
  Map<String, dynamic> standardizeMap(Map<String, dynamic> map) {
    // Créer une copie de la map pour éviter de modifier l'originale
    final standardizedMap = Map<String, dynamic>.from(map);

    // Appliquer toutes les transformations sur la copie
    standardizeId(standardizedMap);
    standardizeRoles(standardizedMap);
    standardizeBooleans(standardizedMap);
    standardizeFieldNames(standardizedMap);

    return standardizedMap;
  }
}
