import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:seatrace/models/fish_classification_result.dart';
import 'package:seatrace/services/fish_recognition_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/widgets/fish_classification_result_widget.dart';
import 'package:seatrace/widgets/optimized_image.dart';

/// Écran pour la classification des poissons
class FishClassificationScreen extends StatefulWidget {
  /// Callback appelé lorsque l'utilisateur sélectionne une espèce
  final Function(String especeNom)? onEspeceSelected;

  /// Indique si l'écran doit retourner l'espèce sélectionnée
  final bool returnResult;

  /// Image du poisson à classifier (optionnelle)
  final File? imageFile;

  const FishClassificationScreen({
    super.key,
    this.onEspeceSelected,
    this.returnResult = true,
    this.imageFile,
  });

  @override
  State<FishClassificationScreen> createState() =>
      _FishClassificationScreenState();
}

class _FishClassificationScreenState extends State<FishClassificationScreen> {
  File? _imageFile;
  bool _isProcessing = false;
  FishClassificationResult? _classificationResult;
  String? _selectedEspece;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Si une image a été fournie, l'utiliser et lancer la classification
    if (widget.imageFile != null) {
      _imageFile = widget.imageFile;
      // Lancer la classification après le build initial
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _classifyImage();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Classification de poisson')),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// Construit le corps de l'écran
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Image du poisson
          _buildImageSection(),

          // Résultat de la classification
          if (_isProcessing) ...[
            const SizedBox(height: 32),
            Center(
              child: Column(
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                    ),
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Identification de l\'espèce...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ] else if (_errorMessage != null) ...[
            const SizedBox(height: 32),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(
                  alpha: 26,
                ), // 0.1 * 255 = 25.5 ≈ 26
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 77),
                ), // 0.3 * 255 = 76.5 ≈ 77
              ),
              child: Column(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _captureImage,
                    child: const Text('Réessayer'),
                  ),
                ],
              ),
            ),
          ] else if (_classificationResult != null) ...[
            const SizedBox(height: 16),
            FishClassificationResultWidget(
              result: _classificationResult!,
              imageUrl: _imageFile?.path,
              onConfirm: _confirmEspece,
              onSelectAlternative: _selectAlternative,
              showConfirmButton: true,
            ),
          ],
        ],
      ),
    );
  }

  /// Construit la section d'image
  Widget _buildImageSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Image du poisson',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (_imageFile != null) ...[
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: OptimizedImage(
                  imageUrl: _imageFile!.path,
                  height: 300,
                  fit: BoxFit.cover,
                  isFile: true,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _captureImage,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Nouvelle photo'),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _pickImage,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Galerie'),
                  ),
                ],
              ),
            ] else ...[
              Container(
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Icon(Icons.image, size: 64, color: Colors.grey),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _captureImage,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Prendre une photo'),
                  ),
                  ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Choisir une image'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Construit la barre inférieure
  Widget? _buildBottomBar() {
    // Nous n'utilisons plus de barre inférieure, tout est géré par le bouton "Confirmer cette espèce"
    return null;
  }

  /// Capture une image avec la caméra
  Future<void> _captureImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _imageFile = File(image.path);
          _classificationResult = null;
          _selectedEspece = null;
          _errorMessage = null;
        });

        _classifyImage();
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.instance.handleAndShowError(
          context,
          e,
          errorContext: 'Capture d\'image',
          showDialog: true,
        );
      }
    }
  }

  /// Sélectionne une image depuis la galerie
  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _imageFile = File(image.path);
          _classificationResult = null;
          _selectedEspece = null;
          _errorMessage = null;
        });

        _classifyImage();
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.instance.handleAndShowError(
          context,
          e,
          errorContext: 'Sélection d\'image',
          showDialog: true,
        );
      }
    }
  }

  /// Classifie l'image sélectionnée
  Future<void> _classifyImage() async {
    if (_imageFile == null) return;

    if (!mounted) return;
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      // Vérifier si l'image existe et est valide
      if (!await _imageFile!.exists()) {
        if (!mounted) return;
        setState(() {
          _isProcessing = false;
          _errorMessage =
              'Le fichier image n\'existe pas. Veuillez prendre une nouvelle photo.';
        });
        return;
      }

      // Simuler un court délai pour donner l'impression d'un traitement
      await Future.delayed(const Duration(milliseconds: 800));

      // Classifier l'image
      final result = await FishRecognitionService().classifyFish(_imageFile!);

      if (!mounted) return;

      if (result == null) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              'Impossible de classifier cette image. Veuillez réessayer avec une autre photo.';
        });
        return;
      }

      // Utiliser directement le nom de l'espèce du résultat de classification
      final especeNom = result.espece;

      if (!mounted) return;

      setState(() {
        _classificationResult = result;
        _selectedEspece = especeNom;
        _isProcessing = false;
        _errorMessage = null;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
        _errorMessage =
            'Une erreur est survenue lors de la classification: ${e.toString()}';
      });

      ErrorHandler.instance.logError(e, context: 'Classification d\'image');
    }
  }

  /// Confirme l'espèce sélectionnée
  void _confirmEspece(String espece) {
    if (_selectedEspece != null) {
      if (widget.onEspeceSelected != null) {
        widget.onEspeceSelected!(_selectedEspece!);
      }
      if (widget.returnResult) {
        Navigator.of(context).pop(_selectedEspece);
      }
    }
  }

  /// Méthode vide pour la compatibilité avec le widget FishClassificationResultWidget
  void _selectAlternative(FishClassificationResult alternative) {
    // Cette méthode est intentionnellement vide car nous n'affichons plus d'alternatives
  }
}
