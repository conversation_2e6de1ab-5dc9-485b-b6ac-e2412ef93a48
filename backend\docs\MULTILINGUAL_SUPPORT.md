# Support Multilingue Backend - SeaTrace

## Vue d'ensemble

Le backend SeaTrace supporte maintenant le multilingue (français et arabe) pour tous les messages d'API, erreurs et notifications.

## Architecture

### Service de Traduction
- **Fichier**: `src/services/translationService.js`
- **Langues supportées**: Français (fr), Arabe (ar)
- **Fallback**: Français par défaut

### Détection de la Langue

Le service détecte automatiquement la langue préférée via :

1. **Header Accept-Language** : `Accept-Language: ar` ou `Accept-Language: fr`
2. **Header personnalisé** : `X-Language: ar` ou `X-Lang: fr`
3. **Paramètre de requête** : `?lang=ar` ou `?lang=fr`
4. **Fallback** : Français par défaut

## Utilisation

### Dans les Contrôleurs

```javascript
// Utilisation avec clé de traduction
res.success(data, null, 200, 'success.operationSuccess');
res.error(null, 400, null, 'error.invalidRequest');

// Utilisation avec message personnalisé
res.success(data, 'Message personnalisé');
res.error('Message d\'erreur personnalisé', 400);
```

### Dans les Middlewares

```javascript
const TranslationService = require('../services/translationService');

// Obtenir la langue de la requête
const lang = TranslationService.getLanguageFromRequest(req);

// Traduire un message
const message = TranslationService.translate('error.userNotFound', lang);
```

## Clés de Traduction Disponibles

### Messages de Succès (`success.*`)
- `operationSuccess` - Opération réussie
- `resourceCreated` - Ressource créée avec succès
- `userAuthenticated` - Utilisateur authentifié
- `passwordChanged` - Mot de passe mis à jour
- `lotApproved` - Lot approuvé
- `auctionStarted` - Enchère démarrée

### Messages d'Erreur (`error.*`)
- `invalidRequest` - Requête invalide
- `authenticationRequired` - Authentification requise
- `accessDenied` - Accès refusé
- `userNotFound` - Utilisateur non trouvé
- `incorrectCredentials` - Email ou mot de passe incorrect
- `passwordTooShort` - Mot de passe trop court
- `emailExists` - Email déjà utilisé

## Exemples d'Utilisation

### Côté Client (Headers)

```javascript
// Requête en arabe
fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Language': 'ar'
  },
  body: JSON.stringify({ email, password })
});

// Requête en français
fetch('/api/lots', {
  headers: {
    'Accept-Language': 'fr'
  }
});
```

### Réponses API

```json
// Réponse en français
{
  "success": true,
  "message": "Utilisateur authentifié",
  "data": { ... }
}

// Réponse en arabe
{
  "success": true,
  "message": "تم التحقق من المستخدم",
  "data": { ... }
}
```

## Ajout de Nouvelles Traductions

### 1. Modifier le Service de Traduction

```javascript
// Dans src/services/translationService.js
const translations = {
  fr: {
    success: {
      newKey: 'Nouveau message en français'
    }
  },
  ar: {
    success: {
      newKey: 'رسالة جديدة بالعربية'
    }
  }
};
```

### 2. Utiliser la Nouvelle Clé

```javascript
res.success(data, null, 200, 'success.newKey');
```

## Bonnes Pratiques

1. **Toujours utiliser les clés de traduction** pour les messages standards
2. **Tester avec les deux langues** lors du développement
3. **Ajouter les traductions en même temps** que les nouvelles fonctionnalités
4. **Utiliser des messages descriptifs** pour faciliter la traduction
5. **Respecter la direction RTL** pour l'arabe côté frontend

## Tests

### Test Manuel

```bash
# Test en français
curl -H "X-Language: fr" http://localhost:3000/api/auth/check

# Test en arabe
curl -H "X-Language: ar" http://localhost:3000/api/auth/check
```

### Test avec Postman

1. Ajouter le header `X-Language` avec la valeur `ar` ou `fr`
2. Vérifier que les messages de réponse sont dans la bonne langue

## Migration des Contrôleurs Existants

Pour migrer un contrôleur existant :

1. Remplacer les messages hardcodés par des clés de traduction
2. Utiliser `res.success()` et `res.error()` avec les clés
3. Tester avec les deux langues

### Avant
```javascript
res.status(200).json({
  success: true,
  message: 'Opération réussie',
  data: result
});
```

### Après
```javascript
res.success(result, null, 200, 'success.operationSuccess');
```

## Support Technique

Pour toute question sur l'implémentation multilingue :
1. Consulter ce document
2. Vérifier les exemples dans `src/routes/authRoutes.js`
3. Tester avec les headers appropriés
