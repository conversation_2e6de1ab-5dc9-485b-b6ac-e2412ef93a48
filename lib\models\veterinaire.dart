import 'package:seatrace/models/base_user.dart';

/// Modèle représentant un vétérinaire dans l'application
/// Correspond au modèle Veterinaire côté backend
class Veterinaire extends BaseUser {
  final String? cin;
  final String? specialite;
  final String? matricule;
  final String? port;

  Veterinaire({
    super.id,
    required super.email,
    required super.roles,
    required super.password,
    required super.nom,
    required super.prenom,
    super.telephone,
    super.photo,
    super.isValidated = false,
    super.isBlocked = false,
    super.createdAt,
    super.updatedAt,
    this.cin,
    this.specialite,
    this.matricule,
    this.port,
  });

  factory Veterinaire.fromMap(Map<String, dynamic> map) {
    return Veterinaire(
      id: map['_id']?.toString() ?? map['id']?.toString(),
      email: map['email'] ?? '',
      roles: map['roles'] ?? '',
      password: map['password'] ?? '',
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      cin: map['cin'],
      telephone: map['telephone']?.toString(),
      photo: map['photo'],
      specialite: map['specialite'],
      matricule: map['matricule'],
      port: map['port'],
      isValidated: map['isValidated'] ?? map['isValid'] ?? false,
      isBlocked: map['isBlocked'] ?? false,
      createdAt: BaseUser.parseDateTime(map['createdAt']),
      updatedAt: BaseUser.parseDateTime(map['updatedAt']),
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'roles': roles,
      'password': password,
      'nom': nom,
      'prenom': prenom,
      'cin': cin,
      'telephone': telephone,
      'photo': photo,
      'specialite': specialite,
      'matricule': matricule,
      'port': port,
      'isValidated': isValidated,
      'isBlocked': isBlocked,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    }..removeWhere((key, value) => value == null);
  }

  /// Créer une copie de l'objet avec des modifications
  Veterinaire copyWith({
    String? id,
    String? email,
    String? roles,
    String? password,
    String? nom,
    String? prenom,
    String? cin,
    String? telephone,
    String? photo,
    String? specialite,
    String? matricule,
    String? port,
    bool? isValidated,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Veterinaire(
      id: id ?? this.id,
      email: email ?? this.email,
      roles: roles ?? this.roles,
      password: password ?? this.password,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      cin: cin ?? this.cin,
      telephone: telephone ?? this.telephone,
      photo: photo ?? this.photo,
      specialite: specialite ?? this.specialite,
      matricule: matricule ?? this.matricule,
      port: port ?? this.port,
      isValidated: isValidated ?? this.isValidated,
      isBlocked: isBlocked ?? this.isBlocked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
