import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/utils/error_handler.dart';

import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';

class PendingUsersScreen extends StatefulWidget {
  const PendingUsersScreen({super.key});

  @override
  State<PendingUsersScreen> createState() => _PendingUsersScreenState();
}

class _PendingUsersScreenState extends State<PendingUsersScreen> {
  final _responsiveService = ResponsiveService();

  bool _isLoading = true;
  String? _errorMessage;
  List<Map<String, dynamic>> _pendingUsers = [];

  @override
  void initState() {
    super.initState();
    _loadPendingUsers();
  }

  Future<void> _loadPendingUsers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await UnifiedApiService().get('admins/pending-users');
      final pendingUsersData = response['data'];
      final pendingUsers =
          pendingUsersData is List ? pendingUsersData : <dynamic>[];

      setState(() {
        _pendingUsers =
            pendingUsers.map((user) {
              // Déterminer le type d'utilisateur
              String userType = 'Utilisateur';
              if (user['roles'].toString().contains('PECHEUR')) {
                userType = 'Pêcheur';
              } else if (user['roles'].toString().contains('VETERINAIRE')) {
                userType = 'Vétérinaire';
              } else if (user['roles'].toString().contains('MARYEUR')) {
                userType = 'Mareyeur';
              } else if (user['roles'].toString().contains('CLIENT')) {
                userType = 'Client';
              }

              return {
                'id': user['id'] ?? user['_id'],
                'nom': user['nom'] ?? '',
                'prenom': user['prenom'] ?? '',
                'email': user['email'] ?? '',
                'telephone': user['telephone'] ?? '',
                'type': userType,
                'photo': user['photo'],
                'createdAt':
                    user['createdAt'] != null
                        ? DateTime.parse(user['createdAt'])
                        : DateTime.now(),
              };
            }).toList();

        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'PendingUsersScreen._loadPendingUsers',
      );
      setState(() {
        _errorMessage =
            'Erreur lors du chargement des utilisateurs: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _validateUser(String userId) async {
    try {
      await UnifiedApiService().post('admins/validate/$userId', {});

      // Mettre à jour la liste des utilisateurs en attente
      await _loadPendingUsers();

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Utilisateur validé avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'PendingUsersScreen._validateUser',
      );
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de la validation: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('Erreur', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Une erreur inconnue est survenue',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPendingUsers,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle_outline,
              color: Colors.green,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun utilisateur en attente',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              'Tous les utilisateurs ont été validés',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Utilisateurs en attente'),
        elevation: 0,
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? _buildErrorView()
                : _pendingUsers.isEmpty
                ? _buildEmptyView()
                : RefreshIndicator(
                  onRefresh: _loadPendingUsers,
                  child: ListView.builder(
                    padding: _responsiveService.adaptivePadding(context),
                    itemCount: _pendingUsers.length,
                    itemBuilder: (context, index) {
                      final user = _pendingUsers[index];
                      final userName = '${user['prenom']} ${user['nom']}';
                      final userType = user['type'];
                      final userEmail = user['email'];
                      final userPhone = user['telephone'];
                      final userPhoto = user['photo'];
                      final createdAt = user['createdAt'] as DateTime;

                      // Formater la date de création
                      final formattedDate =
                          '${createdAt.day}/${createdAt.month}/${createdAt.year}';

                      return SeaCard(
                        margin: const EdgeInsets.symmetric(vertical: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  // Photo de profil ou initiales
                                  SeaAvatar(
                                    imageUrl: userPhoto,
                                    initials:
                                        userName.isNotEmpty
                                            ? userName
                                                .split(' ')
                                                .map(
                                                  (e) =>
                                                      e.isNotEmpty ? e[0] : '',
                                                )
                                                .join()
                                            : '?',
                                    size: 50,
                                    backgroundColor: Theme.of(
                                      context,
                                    ).primaryColor.withValues(alpha: 0.1),
                                    foregroundColor:
                                        Theme.of(context).primaryColor,
                                  ),
                                  const SizedBox(width: 16),
                                  // Informations utilisateur
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          userName,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          userType,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodyMedium?.copyWith(
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.secondary,
                                          ),
                                        ),
                                        if (userEmail.isNotEmpty) ...[
                                          const SizedBox(height: 4),
                                          Text(
                                            userEmail,
                                            style:
                                                Theme.of(
                                                  context,
                                                ).textTheme.bodySmall,
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              // Informations supplémentaires
                              Row(
                                children: [
                                  Icon(
                                    Icons.phone,
                                    size: 16,
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.bodySmall?.color,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    userPhone.isNotEmpty
                                        ? userPhone
                                        : 'Non renseigné',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                  const SizedBox(width: 16),
                                  Icon(
                                    Icons.calendar_today,
                                    size: 16,
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.bodySmall?.color,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Inscrit le $formattedDate',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              // Boutons d'action
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  SeaButton.outline(
                                    text: 'Détails',
                                    icon: Icons.info_outline,
                                    onPressed: () {
                                      // Afficher les détails de l'utilisateur
                                    },
                                  ),
                                  const SizedBox(width: 8),
                                  SeaButton.primary(
                                    text: 'Valider',
                                    icon: Icons.check,
                                    onPressed: () => _validateUser(user['id']),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
      ),
    );
  }
}
