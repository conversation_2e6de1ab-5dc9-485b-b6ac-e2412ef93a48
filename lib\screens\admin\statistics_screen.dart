import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';
import 'package:fl_chart/fl_chart.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final _responsiveService = ResponsiveService();

  bool _isLoading = true;
  String? _errorMessage;

  // Statistiques générales
  int _totalUsers = 0;
  int _totalPecheurs = 0;
  int _totalVeterinaires = 0;
  int _totalMaryeurs = 0;
  int _totalClients = 0;
  int _totalLots = 0;
  int _totalPrises = 0;
  int _totalEncheres = 0;

  // Données pour les graphiques
  List<Map<String, dynamic>> _lotsParMois = [];
  List<Map<String, dynamic>> _ventesParMois = [];

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Appeler l'API pour récupérer les statistiques administratives
      final apiService = UnifiedApiService();
      final response = await apiService.get('stats/admin');

      // Extraire les données de la réponse
      final data = response['data'] ?? {};

      // Statistiques générales
      final stats = {
        'totalUsers': data['totalUsers'] ?? 0,
        'totalPecheurs': data['totalPecheurs'] ?? 0,
        'totalVeterinaires': data['totalVeterinaires'] ?? 0,
        'totalMaryeurs': data['totalMaryeurs'] ?? 0,
        'totalClients': data['totalClients'] ?? 0,
        'totalLots': data['totalLots'] ?? 0,
        'totalPrises': data['totalPrises'] ?? 0,
        'totalEncheres': data['totalEncheres'] ?? 0,
      };

      // Données pour les graphiques
      List<Map<String, dynamic>> lotsParMois = [];
      if (data['lotsParMois'] != null && data['lotsParMois'] is List) {
        // Convertir les données de l'API au format attendu par le graphique
        lotsParMois =
            (data['lotsParMois'] as List).map((item) {
              return {'mois': item['mois'], 'valeur': item['count']};
            }).toList();
      } else {
        // Données par défaut si l'API ne renvoie pas de données
        lotsParMois = [
          {'mois': 'Jan', 'valeur': 12},
          {'mois': 'Fév', 'valeur': 15},
          {'mois': 'Mar', 'valeur': 18},
          {'mois': 'Avr', 'valeur': 20},
          {'mois': 'Mai', 'valeur': 23},
          {'mois': 'Juin', 'valeur': 25},
          {'mois': 'Juil', 'valeur': 30},
          {'mois': 'Août', 'valeur': 28},
          {'mois': 'Sep', 'valeur': 26},
          {'mois': 'Oct', 'valeur': 22},
          {'mois': 'Nov', 'valeur': 19},
          {'mois': 'Déc', 'valeur': 16},
        ];
      }

      List<Map<String, dynamic>> ventesParMois = [];
      if (data['ventesParMois'] != null && data['ventesParMois'] is List) {
        // Convertir les données de l'API au format attendu par le graphique
        ventesParMois =
            (data['ventesParMois'] as List).map((item) {
              return {'mois': item['mois'], 'valeur': item['montant']};
            }).toList();
      } else {
        // Données par défaut si l'API ne renvoie pas de données
        ventesParMois = [
          {'mois': 'Jan', 'valeur': 5000},
          {'mois': 'Fév', 'valeur': 6200},
          {'mois': 'Mar', 'valeur': 7500},
          {'mois': 'Avr', 'valeur': 8100},
          {'mois': 'Mai', 'valeur': 9300},
          {'mois': 'Juin', 'valeur': 10500},
          {'mois': 'Juil', 'valeur': 12000},
          {'mois': 'Août', 'valeur': 11500},
          {'mois': 'Sep', 'valeur': 10800},
          {'mois': 'Oct', 'valeur': 9200},
          {'mois': 'Nov', 'valeur': 8000},
          {'mois': 'Déc', 'valeur': 7200},
        ];
      }

      setState(() {
        _totalUsers = stats['totalUsers'] ?? 0;
        _totalPecheurs = stats['totalPecheurs'] ?? 0;
        _totalVeterinaires = stats['totalVeterinaires'] ?? 0;
        _totalMaryeurs = stats['totalMaryeurs'] ?? 0;
        _totalClients = stats['totalClients'] ?? 0;
        _totalLots = stats['totalLots'] ?? 0;
        _totalPrises = stats['totalPrises'] ?? 0;
        _totalEncheres = stats['totalEncheres'] ?? 0;

        _lotsParMois = lotsParMois;
        _ventesParMois = ventesParMois;

        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'StatisticsScreen._loadStatistics',
      );
      setState(() {
        _errorMessage =
            'Erreur lors du chargement des statistiques: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('Erreur', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Une erreur inconnue est survenue',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadStatistics,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isPhone = _responsiveService.isPhone(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Statistiques'), elevation: 0),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? _buildErrorView()
                : RefreshIndicator(
                  onRefresh: _loadStatistics,
                  child: SingleChildScrollView(
                    padding: _responsiveService.adaptivePadding(context),
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Statistiques des utilisateurs
                        SeaSectionHeader(
                          title: 'Utilisateurs',
                          icon: Icons.people,
                        ),
                        _buildUserStatistics(isPhone),

                        // Statistiques des lots et prises
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Lots et prises',
                          icon: Icons.inventory_2,
                        ),
                        _buildLotsStatistics(isPhone),

                        // Graphique des lots par mois
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Lots par mois',
                          icon: Icons.bar_chart,
                        ),
                        _buildLotsChart(),

                        // Graphique des ventes par mois
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Ventes par mois (en dinars)',
                          icon: Icons.monetization_on,
                        ),
                        _buildVentesChart(),

                        // Espace en bas
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }

  Widget _buildUserStatistics(bool isPhone) {
    if (isPhone) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: SeaStatCard(
                  label: 'Total',
                  value: _totalUsers.toString(),
                  icon: Icons.people,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SeaStatCard(
                  label: 'Pêcheurs',
                  value: _totalPecheurs.toString(),
                  icon: Icons.sailing,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: SeaStatCard(
                  label: 'Vétérinaires',
                  value: _totalVeterinaires.toString(),
                  icon: Icons.medical_services,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SeaStatCard(
                  label: 'Mareyeurs',
                  value: _totalMaryeurs.toString(),
                  icon: Icons.business,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SeaStatCard(
            label: 'Clients',
            value: _totalClients.toString(),
            icon: Icons.shopping_cart,
            color: Colors.purple,
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: SeaStatCard(
              label: 'Total',
              value: _totalUsers.toString(),
              icon: Icons.people,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SeaStatCard(
              label: 'Pêcheurs',
              value: _totalPecheurs.toString(),
              icon: Icons.sailing,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SeaStatCard(
              label: 'Vétérinaires',
              value: _totalVeterinaires.toString(),
              icon: Icons.medical_services,
              color: Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SeaStatCard(
              label: 'Mareyeurs',
              value: _totalMaryeurs.toString(),
              icon: Icons.business,
              color: Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SeaStatCard(
              label: 'Clients',
              value: _totalClients.toString(),
              icon: Icons.shopping_cart,
              color: Colors.purple,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildLotsStatistics(bool isPhone) {
    if (isPhone) {
      return Column(
        children: [
          SeaStatCard(
            label: 'Lots',
            value: _totalLots.toString(),
            icon: Icons.inventory_2,
            color: Theme.of(context).colorScheme.secondary,
          ),
          const SizedBox(height: 12),
          SeaStatCard(
            label: 'Prises',
            value: _totalPrises.toString(),
            icon: Icons.catching_pokemon,
            color: Colors.teal,
          ),
          const SizedBox(height: 12),
          SeaStatCard(
            label: 'Enchères',
            value: _totalEncheres.toString(),
            icon: Icons.gavel,
            color: Colors.amber,
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: SeaStatCard(
              label: 'Lots',
              value: _totalLots.toString(),
              icon: Icons.inventory_2,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SeaStatCard(
              label: 'Prises',
              value: _totalPrises.toString(),
              icon: Icons.catching_pokemon,
              color: Colors.teal,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SeaStatCard(
              label: 'Enchères',
              value: _totalEncheres.toString(),
              icon: Icons.gavel,
              color: Colors.amber,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildLotsChart() {
    return SeaCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
          height: 300,
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY:
                  _lotsParMois
                      .map((e) => e['valeur'] as int)
                      .reduce((a, b) => a > b ? a : b) *
                  1.2,
              barTouchData: BarTouchData(
                enabled: true,
                touchTooltipData: BarTouchTooltipData(
                  tooltipBgColor: Colors.blueGrey.withValues(alpha: 0.8),
                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                    return BarTooltipItem(
                      '${_lotsParMois[groupIndex]['mois']}: ${rod.toY.round()} lots',
                      const TextStyle(color: Colors.white),
                    );
                  },
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= _lotsParMois.length) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          _lotsParMois[value.toInt()]['mois'],
                          style: const TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30,
                    getTitlesWidget: (value, meta) {
                      if (value == 0) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Text(
                          value.toInt().toString(),
                          style: const TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
              ),
              gridData: FlGridData(
                show: true,
                horizontalInterval: 5,
                getDrawingHorizontalLine: (value) {
                  return FlLine(
                    color: Colors.grey.withValues(alpha: 0.2),
                    strokeWidth: 1,
                  );
                },
              ),
              borderData: FlBorderData(show: false),
              barGroups:
                  _lotsParMois.asMap().entries.map((entry) {
                    final index = entry.key;
                    final data = entry.value;
                    return BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: data['valeur'].toDouble(),
                          color: Theme.of(context).colorScheme.secondary,
                          width: 16,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVentesChart() {
    return SeaCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
          height: 300,
          child: LineChart(
            LineChartData(
              lineTouchData: LineTouchData(
                enabled: true,
                touchTooltipData: LineTouchTooltipData(
                  tooltipBgColor: Colors.blueGrey.withValues(alpha: 0.8),
                  getTooltipItems: (touchedSpots) {
                    return touchedSpots.map((spot) {
                      final mois = _ventesParMois[spot.x.toInt()]['mois'];
                      return LineTooltipItem(
                        '$mois: ${spot.y.toInt()} dinars',
                        const TextStyle(color: Colors.white),
                      );
                    }).toList();
                  },
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= _ventesParMois.length) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          _ventesParMois[value.toInt()]['mois'],
                          style: const TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40,
                    getTitlesWidget: (value, meta) {
                      if (value == 0) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Text(
                          '${(value / 1000).toInt()}k',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
              ),
              gridData: FlGridData(
                show: true,
                horizontalInterval: 2000,
                getDrawingHorizontalLine: (value) {
                  return FlLine(
                    color: Colors.grey.withValues(alpha: 0.2),
                    strokeWidth: 1,
                  );
                },
              ),
              borderData: FlBorderData(show: false),
              lineBarsData: [
                LineChartBarData(
                  spots:
                      _ventesParMois.asMap().entries.map((entry) {
                        final index = entry.key;
                        final data = entry.value;
                        return FlSpot(
                          index.toDouble(),
                          data['valeur'].toDouble(),
                        );
                      }).toList(),
                  isCurved: true,
                  color: Theme.of(context).primaryColor,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: const FlDotData(show: true),
                  belowBarData: BarAreaData(
                    show: true,
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.2),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
