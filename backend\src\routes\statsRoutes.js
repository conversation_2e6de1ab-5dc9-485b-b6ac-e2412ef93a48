/**
 * Routes pour les statistiques
 */
const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { isAdmin } = require('../middleware/adminAuth');
const { getDashboardStats, getPecheurStats, getMaryeurStats } = require('../controllers/statsController');
const mongoose = require('mongoose');

/**
 * @route GET /api/stats/dashboard
 * @desc Récupérer les statistiques générales du tableau de bord
 * @access Private
 */
router.get('/dashboard', auth, getDashboardStats);

/**
 * @route GET /api/stats/pecheur/:id
 * @desc Récupérer les statistiques d'un pêcheur spécifique
 * @access Private
 */
router.get('/pecheur/:id', auth, getPecheurStats);

/**
 * @route GET /api/stats/maryeur/:id
 * @desc Récupérer les statistiques d'un maryeur spécifique
 * @access Private
 */
router.get('/maryeur/:id', auth, getMaryeurStats);

/**
 * @route GET /api/stats/veterinaire/:id
 * @desc Récupérer les statistiques d'un vétérinaire spécifique
 * @access Private
 * @note Les critères de filtrage sont harmonisés avec la route /api/lots/pending
 */
router.get('/veterinaire/:id', auth, async (req, res, next) => {
  try {
    // Récupérer l'ID du vétérinaire
    const veterinaireId = req.params.id;

    // Récupérer les lots associés à ce vétérinaire
    const Lot = require('../models/Lot');

    // Compter les lots en attente, approuvés et rejetés
    // Utiliser les mêmes critères que la route /api/lots/pending pour la cohérence
    const pendingLots = await Lot.countDocuments({
      veterinaire: veterinaireId,
      test: false,
      isValidated: false,
      user: { $exists: true, $ne: null },
      especeNom: { $exists: true, $ne: null },
      photo: { $exists: true, $ne: null }
    });

    const approvedLots = await Lot.countDocuments({
      veterinaire: veterinaireId,
      test: true,
      isValidated: true
    });

    const rejectedLots = await Lot.countDocuments({
      veterinaire: veterinaireId,
      test: true,
      isValidated: false
    });

    // Retourner les statistiques
    res.success({
      pendingLots,
      approvedLots,
      rejectedLots,
      totalLots: pendingLots + approvedLots + rejectedLots
    }, 'Statistiques du vétérinaire récupérées avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/stats/maryeur/:id
 * @desc Récupérer les statistiques d'un mareyeur spécifique
 * @access Private
 * @note Les critères de filtrage sont harmonisés avec la route /api/lots/pending-price
 */
router.get('/maryeur/:id', auth, async (req, res, next) => {
  try {
    // Récupérer l'ID du mareyeur
    const maryeurId = req.params.id;

    // Récupérer les lots associés à ce mareyeur
    const Lot = require('../models/Lot');

    // Compter les lots en attente de prix (utiliser les mêmes critères que /api/lots/pending-price)
    const pendingLots = await Lot.countDocuments({
      maryeur: maryeurId,
      test: true,
      status: true,
      isValidated: true,
      $or: [
        { prixInitial: { $exists: false } },
        { prixInitial: null }
      ],
      vendu: false,
      user: { $exists: true, $ne: null },
      especeNom: { $exists: true, $ne: null, $ne: '', $ne: 'Poisson' },
      dateSoumission: { $exists: true, $ne: null },
      photo: { $exists: true, $ne: null }
    });

    // Compter les enchères actives
    const activeAuctions = await Lot.countDocuments({
      maryeur: maryeurId,
      prixInitial: { $exists: true, $ne: null },
      vendu: false,
      test: true,
      status: true
    });

    // Compter les enchères complétées
    const completedAuctions = await Lot.countDocuments({
      maryeur: maryeurId,
      vendu: true
    });

    // Retourner les statistiques
    res.success({
      pendingLots,
      activeAuctions,
      completedAuctions,
      totalLots: pendingLots + activeAuctions + completedAuctions
    }, 'Statistiques du mareyeur récupérées avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/stats/client/:id
 * @desc Récupérer les statistiques d'un client spécifique
 * @access Private
 */
router.get('/client/:id', auth, async (req, res, next) => {
  try {
    // Récupérer l'ID du client
    const clientId = req.params.id;

    // Récupérer les lots achetés par ce client
    const Lot = require('../models/Lot');

    // Compter les achats et les enchères actives
    const purchases = await Lot.countDocuments({
      acheteur: clientId,
      vendu: true
    });

    const activeAuctions = await Lot.countDocuments({
      prixInitial: { $exists: true, $ne: null },
      test: true,
      status: true,
      vendu: false
    });

    // Retourner les statistiques
    res.success({
      purchases,
      activeAuctions
    }, 'Statistiques du client récupérées avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/stats/admin
 * @desc Récupérer les statistiques pour le tableau de bord administrateur
 * @access Private (Admin uniquement)
 */
router.get('/admin', isAdmin, async (req, res, next) => {
  try {
    // Importer les modèles nécessaires
    const Client = require('../models/Client');
    const Pecheur = require('../models/Pecheur');
    const Veterinaire = require('../models/Veterinaire');
    const Maryeur = require('../models/Maryeur');
    const Admin = require('../models/Admin');
    const Lot = require('../models/Lot');
    const Prise = require('../models/Prise');

    // Compter les utilisateurs par type
    const totalClients = await Client.countDocuments();
    const totalPecheurs = await Pecheur.countDocuments();
    const totalVeterinaires = await Veterinaire.countDocuments();
    const totalMaryeurs = await Maryeur.countDocuments();
    const totalAdmins = await Admin.countDocuments();
    const totalUsers = totalClients + totalPecheurs + totalVeterinaires + totalMaryeurs + totalAdmins;

    // Compter les utilisateurs en attente de validation
    const pendingClients = await Client.countDocuments({ isValidated: false });
    const pendingPecheurs = await Pecheur.countDocuments({ isValidated: false });
    const pendingVeterinaires = await Veterinaire.countDocuments({ isValidated: false });
    const pendingMaryeurs = await Maryeur.countDocuments({ isValidated: false });
    const totalPendingUsers = pendingClients + pendingPecheurs + pendingVeterinaires + pendingMaryeurs;

    // Compter les utilisateurs bloqués
    const blockedClients = await Client.countDocuments({ isBlocked: true });
    const blockedPecheurs = await Pecheur.countDocuments({ isBlocked: true });
    const blockedVeterinaires = await Veterinaire.countDocuments({ isBlocked: true });
    const blockedMaryeurs = await Maryeur.countDocuments({ isBlocked: true });
    const totalBlockedUsers = blockedClients + blockedPecheurs + blockedVeterinaires + blockedMaryeurs;

    // Compter les lots et les prises
    const totalLots = await Lot.countDocuments();
    const totalPrises = await Prise.countDocuments();
    const totalEncheres = await Lot.countDocuments({ prixInitial: { $exists: true, $ne: null } });

    // Générer des données pour les graphiques (exemple)
    const lotsParMois = [
      { mois: 'Jan', count: 12 },
      { mois: 'Fév', count: 19 },
      { mois: 'Mar', count: 25 },
      { mois: 'Avr', count: 18 },
      { mois: 'Mai', count: 22 },
      { mois: 'Juin', count: 30 },
      { mois: 'Juil', count: 35 },
      { mois: 'Août', count: 28 },
      { mois: 'Sep', count: 20 },
      { mois: 'Oct', count: 15 },
      { mois: 'Nov', count: 23 },
      { mois: 'Déc', count: 17 }
    ];

    const ventesParMois = [
      { mois: 'Jan', montant: 12000 },
      { mois: 'Fév', montant: 19000 },
      { mois: 'Mar', montant: 25000 },
      { mois: 'Avr', montant: 18000 },
      { mois: 'Mai', montant: 22000 },
      { mois: 'Juin', montant: 30000 },
      { mois: 'Juil', montant: 35000 },
      { mois: 'Août', montant: 28000 },
      { mois: 'Sep', montant: 20000 },
      { mois: 'Oct', montant: 15000 },
      { mois: 'Nov', montant: 23000 },
      { mois: 'Déc', montant: 17000 }
    ];

    // Retourner les statistiques
    res.success({
      totalUsers,
      totalClients,
      totalPecheurs,
      totalVeterinaires,
      totalMaryeurs,
      totalAdmins,
      totalPendingUsers,
      totalBlockedUsers,
      totalLots,
      totalPrises,
      totalEncheres,
      lotsParMois,
      ventesParMois
    }, 'Statistiques administratives récupérées avec succès');
  } catch (error) {
    next(error);
  }
});

module.exports = router;
