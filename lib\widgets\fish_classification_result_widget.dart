import 'package:flutter/material.dart';
import 'package:seatrace/models/fish_classification_result.dart';

/// Widget pour afficher les résultats de classification d'un poisson
class FishClassificationResultWidget extends StatelessWidget {
  /// Résultat de la classification
  final FishClassificationResult result;

  /// URL de l'image du poisson
  final String? imageUrl;

  /// Callback appelé lorsque l'utilisateur confirme l'espèce
  final Function(String espece)? onConfirm;

  /// Callback appelé lorsque l'utilisateur sélectionne une alternative
  final Function(FishClassificationResult alternative)? onSelectAlternative;

  /// Indique si le widget doit afficher un bouton de confirmation
  final bool showConfirmButton;

  const FishClassificationResultWidget({
    super.key,
    required this.result,
    this.imageUrl,
    this.onConfirm,
    this.onSelectAlternative,
    this.showConfirmButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Titre
            Text(
              'Espèce identifiée',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Résultat principal (sans afficher l'image)
            _buildMainResult(context),

            // Bouton de confirmation
            if (showConfirmButton && onConfirm != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => onConfirm!(result.espece),
                icon: const Icon(Icons.check_circle),
                label: const Text('Confirmer cette espèce'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Construit le widget pour afficher le résultat principal
  Widget _buildMainResult(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 13), // 0.05 * 255 ≈ 13
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  result.espece,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          if (result.nomScientifique != null &&
              result.nomScientifique!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              result.nomScientifique!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.secondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              '100%',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Méthode _getConfidenceColor supprimée car elle n'est plus nécessaire
}
