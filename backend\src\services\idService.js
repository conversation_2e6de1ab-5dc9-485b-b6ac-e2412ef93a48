/**
 * Service centralisé pour la gestion des IDs
 * Ce service fournit des méthodes pour standardiser les IDs entre le frontend et le backend
 */

const mongoose = require('mongoose');

/**
 * Classe de service pour la gestion des IDs
 */
class IdService {
  /**
   * Vérifie si un ID est un ObjectId MongoDB valide
   * @param {string} id - L'ID à vérifier
   * @returns {boolean} - true si l'ID est un ObjectId valide, false sinon
   */
  isValidObjectId(id) {
    return mongoose.Types.ObjectId.isValid(id);
  }

  /**
   * Convertit un ID en ObjectId MongoDB si possible
   * @param {string} id - L'ID à convertir
   * @returns {ObjectId|string} - L'ObjectId si la conversion est possible, sinon l'ID original
   */
  toObjectId(id) {
    if (this.isValidObjectId(id)) {
      return new mongoose.Types.ObjectId(id);
    }
    return id;
  }

  /**
   * Standardise un ID pour le frontend (toujours en string)
   * @param {ObjectId|string} id - L'ID à standardiser
   * @returns {string} - L'ID standardisé
   */
  toFrontendId(id) {
    if (!id) return null;
    return id.toString();
  }

  /**
   * Recherche une entité par ID (ObjectId ou ID personnalisé)
   * @param {Model} model - Le modèle Mongoose à utiliser
   * @param {string} id - L'ID à rechercher
   * @param {Object} options - Options supplémentaires (populate, select, etc.)
   * @returns {Promise<Document|null>} - L'entité trouvée ou null
   */
  async findById(model, id, options = {}) {
    let entity = null;
    
    // Essayer de trouver par ObjectId
    if (this.isValidObjectId(id)) {
      entity = await model.findById(id);
    }
    
    // Si non trouvé, essayer de trouver par ID personnalisé
    if (!entity) {
      entity = await model.findOne({ id: id });
    }
    
    // Si toujours non trouvé, essayer de trouver par identifiant (pour les lots)
    if (!entity && model.modelName === 'Lot') {
      entity = await model.findOne({ identifiant: id });
    }
    
    // Appliquer les options (populate, etc.)
    if (entity && options.populate) {
      if (Array.isArray(options.populate)) {
        for (const field of options.populate) {
          entity = await entity.populate(field);
        }
      } else {
        entity = await entity.populate(options.populate);
      }
    }
    
    return entity;
  }

  /**
   * Met à jour une entité par ID (ObjectId ou ID personnalisé)
   * @param {Model} model - Le modèle Mongoose à utiliser
   * @param {string} id - L'ID de l'entité à mettre à jour
   * @param {Object} updateData - Les données de mise à jour
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Document|null>} - L'entité mise à jour ou null
   */
  async updateById(model, id, updateData, options = {}) {
    let entity = null;
    
    // Essayer de mettre à jour par ObjectId
    if (this.isValidObjectId(id)) {
      entity = await model.findByIdAndUpdate(id, updateData, { 
        new: true, 
        runValidators: true,
        ...options 
      });
    }
    
    // Si non trouvé, essayer de mettre à jour par ID personnalisé
    if (!entity) {
      entity = await model.findOneAndUpdate({ id: id }, updateData, { 
        new: true, 
        runValidators: true,
        ...options 
      });
    }
    
    // Si toujours non trouvé, essayer de mettre à jour par identifiant (pour les lots)
    if (!entity && model.modelName === 'Lot') {
      entity = await model.findOneAndUpdate({ identifiant: id }, updateData, { 
        new: true, 
        runValidators: true,
        ...options 
      });
    }
    
    return entity;
  }

  /**
   * Supprime une entité par ID (ObjectId ou ID personnalisé)
   * @param {Model} model - Le modèle Mongoose à utiliser
   * @param {string} id - L'ID de l'entité à supprimer
   * @returns {Promise<Document|null>} - L'entité supprimée ou null
   */
  async deleteById(model, id) {
    let entity = null;
    
    // Essayer de supprimer par ObjectId
    if (this.isValidObjectId(id)) {
      entity = await model.findByIdAndDelete(id);
    }
    
    // Si non trouvé, essayer de supprimer par ID personnalisé
    if (!entity) {
      entity = await model.findOneAndDelete({ id: id });
    }
    
    // Si toujours non trouvé, essayer de supprimer par identifiant (pour les lots)
    if (!entity && model.modelName === 'Lot') {
      entity = await model.findOneAndDelete({ identifiant: id });
    }
    
    return entity;
  }

  /**
   * Standardise les IDs dans un objet ou un tableau
   * @param {Object|Array} data - Les données à standardiser
   * @returns {Object|Array} - Les données avec IDs standardisés
   */
  standardizeIds(data) {
    if (!data) return data;
    
    // Si c'est un tableau, standardiser chaque élément
    if (Array.isArray(data)) {
      return data.map(item => this.standardizeIds(item));
    }
    
    // Si c'est un objet, standardiser ses propriétés
    if (typeof data === 'object' && data !== null) {
      const result = { ...data };
      
      // Si l'objet a un _id, ajouter id
      if (result._id) {
        result.id = this.toFrontendId(result._id);
      }
      
      // Parcourir toutes les propriétés
      Object.keys(result).forEach(key => {
        // Standardiser les propriétés imbriquées
        if (typeof result[key] === 'object' && result[key] !== null) {
          result[key] = this.standardizeIds(result[key]);
        }
        
        // Convertir les ObjectId en chaînes
        if (result[key] instanceof mongoose.Types.ObjectId) {
          result[key] = this.toFrontendId(result[key]);
        }
      });
      
      return result;
    }
    
    return data;
  }
}

// Exporter une instance unique du service
module.exports = new IdService();
