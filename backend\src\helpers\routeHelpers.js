/**
 * Helpers pour standardiser les opérations CRUD dans les routes
 * Ces helpers simplifient l'implémentation des routes et assurent une cohérence
 */

const idService = require('../services/idService');
const { NotFoundError } = require('../middleware/errorHandler');

/**
 * Crée un handler pour récupérer une entité par ID
 * @param {Model} model - Le modèle Mongoose à utiliser
 * @param {Object} options - Options (populate, message, etc.)
 * @returns {Function} - Le handler Express
 */
function createGetByIdHandler(model, options = {}) {
  const {
    populate = [],
    notFoundMessage = `${model.modelName} non trouvé`,
    successMessage = `${model.modelName} récupéré avec succès`
  } = options;
  
  return async (req, res, next) => {
    try {
      const entity = await idService.findById(model, req.params.id, { populate });
      
      if (!entity) {
        throw new NotFoundError(notFoundMessage);
      }
      
      res.success(entity, successMessage);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Crée un handler pour mettre à jour une entité par ID
 * @param {Model} model - Le modèle Mongoose à utiliser
 * @param {Object} options - Options (populate, message, etc.)
 * @returns {Function} - Le handler Express
 */
function createUpdateByIdHandler(model, options = {}) {
  const {
    populate = [],
    notFoundMessage = `${model.modelName} non trouvé`,
    successMessage = `${model.modelName} mis à jour avec succès`,
    preUpdate = (req) => req.body, // Fonction pour transformer les données avant la mise à jour
    postUpdate = (entity) => entity // Fonction pour transformer l'entité après la mise à jour
  } = options;
  
  return async (req, res, next) => {
    try {
      // Préparer les données de mise à jour
      const updateData = preUpdate(req);
      
      // Mettre à jour l'entité
      const entity = await idService.updateById(model, req.params.id, updateData);
      
      if (!entity) {
        throw new NotFoundError(notFoundMessage);
      }
      
      // Appliquer les transformations post-mise à jour
      const result = postUpdate(entity);
      
      // Peupler les relations si nécessaire
      let populatedEntity = result;
      if (populate.length > 0) {
        populatedEntity = await model.findById(entity._id).populate(populate);
      }
      
      res.success(populatedEntity, successMessage);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Crée un handler pour supprimer une entité par ID
 * @param {Model} model - Le modèle Mongoose à utiliser
 * @param {Object} options - Options (message, etc.)
 * @returns {Function} - Le handler Express
 */
function createDeleteByIdHandler(model, options = {}) {
  const {
    notFoundMessage = `${model.modelName} non trouvé`,
    successMessage = `${model.modelName} supprimé avec succès`,
    preDelete = async () => true, // Fonction exécutée avant la suppression
    postDelete = async () => {} // Fonction exécutée après la suppression
  } = options;
  
  return async (req, res, next) => {
    try {
      // Exécuter les actions pré-suppression
      const shouldProceed = await preDelete(req);
      if (!shouldProceed) {
        return res.error('Suppression annulée', 400);
      }
      
      // Supprimer l'entité
      const entity = await idService.deleteById(model, req.params.id);
      
      if (!entity) {
        throw new NotFoundError(notFoundMessage);
      }
      
      // Exécuter les actions post-suppression
      await postDelete(entity);
      
      res.success(null, successMessage);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Crée un handler pour récupérer toutes les entités
 * @param {Model} model - Le modèle Mongoose à utiliser
 * @param {Object} options - Options (filter, sort, populate, etc.)
 * @returns {Function} - Le handler Express
 */
function createGetAllHandler(model, options = {}) {
  const {
    defaultFilter = {},
    defaultSort = { createdAt: -1 },
    populate = [],
    successMessage = `Liste des ${model.modelName.toLowerCase()}s récupérée avec succès`,
    preFind = (req) => ({}), // Fonction pour générer des filtres supplémentaires
    postFind = (entities) => entities // Fonction pour transformer les entités après la recherche
  } = options;
  
  return async (req, res, next) => {
    try {
      // Générer les filtres
      const additionalFilter = preFind(req);
      const filter = { ...defaultFilter, ...additionalFilter };
      
      // Récupérer les entités
      let query = model.find(filter).sort(defaultSort);
      
      // Appliquer les relations à peupler
      if (populate.length > 0) {
        if (Array.isArray(populate)) {
          populate.forEach(field => {
            query = query.populate(field);
          });
        } else {
          query = query.populate(populate);
        }
      }
      
      const entities = await query;
      
      // Appliquer les transformations post-recherche
      const result = postFind(entities);
      
      res.success(result, successMessage);
    } catch (error) {
      next(error);
    }
  };
}

module.exports = {
  createGetByIdHandler,
  createUpdateByIdHandler,
  createDeleteByIdHandler,
  createGetAllHandler
};
