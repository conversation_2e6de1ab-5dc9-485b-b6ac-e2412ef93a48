import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Un widget de champ de texte personnalisé pour l'application SeaTrace
/// avec un style cohérent et des options de personnalisation.
class SeaTextField extends StatefulWidget {
  /// Le contrôleur du champ de texte
  final TextEditingController? controller;

  /// Le texte d'indication (placeholder)
  final String? hint;

  /// Le libellé du champ
  final String? label;

  /// L'icône à afficher à gauche du champ
  final IconData? prefixIcon;

  /// L'icône à afficher à droite du champ
  final IconData? suffixIcon;

  /// L'action à effectuer lorsque l'utilisateur appuie sur l'icône de suffixe
  final VoidCallback? onSuffixPressed;

  /// Le type de clavier à afficher
  final TextInputType keyboardType;

  /// Les validateurs pour le champ
  final String? Function(String?)? validator;

  /// Indique si le champ est obligatoire
  final bool isRequired;

  /// Indique si le champ est en lecture seule
  final bool readOnly;

  /// Indique si le champ est désactivé
  final bool disabled;

  /// Indique si le champ doit masquer le texte (pour les mots de passe)
  final bool obscureText;

  /// Le nombre maximal de caractères
  final int? maxLength;

  /// Le nombre maximal de lignes
  final int? maxLines;

  /// Le nombre minimal de lignes
  final int? minLines;

  /// Les formateurs de texte
  final List<TextInputFormatter>? inputFormatters;

  /// L'action à effectuer lorsque l'utilisateur soumet le champ
  final void Function(String)? onSubmitted;

  /// L'action à effectuer lorsque le texte change
  final void Function(String)? onChanged;

  /// Le texte d'aide à afficher sous le champ
  final String? helperText;

  /// Le texte d'erreur à afficher sous le champ
  final String? errorText;

  /// La couleur du champ
  final Color? color;

  /// La couleur de fond du champ
  final Color? backgroundColor;

  /// Le rayon de la bordure du champ
  final double borderRadius;

  /// La marge externe du champ
  final EdgeInsetsGeometry? margin;

  /// Le rembourrage interne du champ
  final EdgeInsetsGeometry? contentPadding;

  /// Crée une nouvelle instance de [SeaTextField].
  const SeaTextField({
    super.key,
    this.controller,
    this.hint,
    this.label,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixPressed,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.isRequired = false,
    this.readOnly = false,
    this.disabled = false,
    this.obscureText = false,
    this.maxLength,
    this.maxLines = 1,
    this.minLines,
    this.inputFormatters,
    this.onSubmitted,
    this.onChanged,
    this.helperText,
    this.errorText,
    this.color,
    this.backgroundColor,
    this.borderRadius = 10,
    this.margin,
    this.contentPadding,
  });

  @override
  State<SeaTextField> createState() => _SeaTextFieldState();
}

class _SeaTextFieldState extends State<SeaTextField> {
  late bool _obscureText;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = widget.color ?? theme.primaryColor;
    final textLightColor = theme.hintColor;
    final backgroundColor =
        widget.backgroundColor ??
        theme.inputDecorationTheme.fillColor ??
        theme.colorScheme.surface;
    final errorColor = theme.colorScheme.error;

    // Construire le libellé avec l'indicateur d'obligation si nécessaire
    final labelText =
        widget.isRequired && widget.label != null
            ? '${widget.label} *'
            : widget.label;

    // Déterminer l'icône de suffixe
    Widget? suffixIcon;
    if (widget.obscureText) {
      // Icône pour afficher/masquer le mot de passe
      suffixIcon = IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: textLightColor,
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    } else if (widget.suffixIcon != null) {
      // Icône personnalisée avec action
      suffixIcon = IconButton(
        icon: Icon(widget.suffixIcon, color: primaryColor),
        onPressed: widget.onSuffixPressed,
      );
    }

    return Container(
      margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: widget.controller,
        keyboardType: widget.keyboardType,
        obscureText: _obscureText,
        maxLength: widget.maxLength,
        maxLines: widget.maxLines,
        minLines: widget.minLines,
        readOnly: widget.readOnly,
        enabled: !widget.disabled,
        inputFormatters: widget.inputFormatters,
        onFieldSubmitted: widget.onSubmitted,
        onChanged: widget.onChanged,
        validator: widget.validator,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: widget.disabled ? textLightColor : theme.colorScheme.onSurface,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: widget.hint,
          helperText: widget.helperText,
          errorText: widget.errorText,
          filled: true,
          fillColor:
              widget.disabled
                  ? backgroundColor.withValues(alpha: 0.5)
                  : backgroundColor,
          prefixIcon:
              widget.prefixIcon != null
                  ? Icon(widget.prefixIcon, color: textLightColor)
                  : null,
          suffixIcon: suffixIcon,
          contentPadding:
              widget.contentPadding ??
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: textLightColor.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(color: primaryColor, width: 1.5),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(color: errorColor, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(color: errorColor, width: 1.5),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: textLightColor.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
        ),
      ),
    );
  }
}
