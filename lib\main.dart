import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'utils/app_theme.dart';
import 'services/unified_api_service.dart';
import 'services/unified_notification_service.dart';
import 'services/websocket_service.dart';
import 'services/language_service.dart';
import 'l10n/l10n.dart';

import 'screens/video_splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser la configuration des services
  // ServiceConfig est déjà initialisé statiquement

  // Initialiser les services
  UnifiedApiService(); // Initialiser le singleton API
  WebSocketService(); // Initialiser le singleton WebSocket
  await UnifiedNotificationService()
      .initialize(); // Initialiser les notifications
  await LanguageService().initialize(); // Initialiser le service de langue

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _isLoading = true;
  final Widget _initialScreen = const VideoSplashScreen();

  @override
  void initState() {
    super.initState();
    _loadApp();
  }

  Future<void> _loadApp() async {
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Locale>(
      valueListenable: LanguageService().languageNotifier,
      builder: (context, locale, _) {
        return MaterialApp(
          title: 'SeaTrace',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          debugShowCheckedModeBanner: false,

          // Configuration pour l'internationalisation
          locale: locale,
          supportedLocales: L10n.all,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],

          // Support RTL pour l'arabe
          localeResolutionCallback: (locale, supportedLocales) {
            // Si la locale demandée est supportée, l'utiliser
            if (locale != null) {
              for (var supportedLocale in supportedLocales) {
                if (supportedLocale.languageCode == locale.languageCode) {
                  return supportedLocale;
                }
              }
            }
            // Sinon, utiliser le français par défaut
            return const Locale('fr');
          },

          builder: (context, child) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(padding: EdgeInsets.zero),
              child: child!,
            );
          },

          onGenerateRoute: (settings) {
            if (settings.name == null) {
              return MaterialPageRoute(
                builder:
                    (context) =>
                        _isLoading
                            ? const Scaffold(
                              body: Center(child: CircularProgressIndicator()),
                            )
                            : _initialScreen,
              );
            }

            return PageRouteBuilder(
              settings: settings,
              pageBuilder:
                  (context, animation, secondaryAnimation) =>
                      _isLoading
                          ? const Scaffold(
                            body: Center(child: CircularProgressIndicator()),
                          )
                          : _initialScreen,
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeInOut;

                var tween = Tween(
                  begin: begin,
                  end: end,
                ).chain(CurveTween(curve: curve));
                var offsetAnimation = animation.drive(tween);

                return SlideTransition(position: offsetAnimation, child: child);
              },
              transitionDuration: const Duration(milliseconds: 300),
            );
          },

          home:
              _isLoading
                  ? const Scaffold(
                    body: Center(child: CircularProgressIndicator()),
                  )
                  : _initialScreen,
        );
      },
    );
  }
}
