/**
 * Script pour nettoyer les lots avec des données invalides
 */
const mongoose = require('mongoose');
const Lot = require('../models/Lot');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://dorsafayed23:<EMAIL>/seatrace?retryWrites=true&w=majority'; //'mongodb://localhost:27017/seatrace';

async function cleanupInvalidLots() {
  try {
    // Connexion à MongoDB
    console.log('Connexion à MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connecté à MongoDB avec succès');

    // Critères pour identifier les lots invalides
    const invalidLotsFilter = {
      $or: [
        { especeNom: { $in: [null, '', 'Poisson'] } },
        { user: { $in: [null] } },
        { dateSoumission: { $in: [null] } },
        { photo: { $in: [null, ''] } },
        { quantite: { $lte: 0 } },
        { poids: { $lte: 0 } }
      ]
    };

    // Compter les lots invalides avant suppression
    const invalidCount = await Lot.countDocuments(invalidLotsFilter);
    console.log(`Lots invalides trouvés: ${invalidCount}`);

    if (invalidCount === 0) {
      console.log('Aucun lot invalide trouvé');
      return;
    }

    // Afficher quelques exemples de lots invalides
    const sampleInvalidLots = await Lot.find(invalidLotsFilter).limit(5);
    console.log('\nExemples de lots invalides:');
    sampleInvalidLots.forEach((lot, index) => {
      console.log(`${index + 1}. ID: ${lot._id}, Espèce: "${lot.especeNom}", User: ${lot.user}, Date: ${lot.dateSoumission}`);
    });

    // Demander confirmation (en mode automatique pour le script)
    console.log(`\nSuppression de ${invalidCount} lots invalides...`);

    // Supprimer les lots invalides
    const result = await Lot.deleteMany(invalidLotsFilter);
    
    console.log(`✅ ${result.deletedCount} lots invalides ont été supprimés avec succès`);

    // Vérifier qu'il ne reste plus de lots invalides
    const remainingInvalid = await Lot.countDocuments(invalidLotsFilter);
    if (remainingInvalid === 0) {
      console.log('✅ Nettoyage terminé - Aucun lot invalide restant');
    } else {
      console.log(`⚠️  Il reste encore ${remainingInvalid} lots invalides`);
    }

  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  } finally {
    // Fermer la connexion
    await mongoose.disconnect();
    console.log('Connexion MongoDB fermée');
  }
}

// Exécuter le script
if (require.main === module) {
  cleanupInvalidLots()
    .then(() => {
      console.log('Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Erreur fatale:', error);
      process.exit(1);
    });
}

module.exports = { cleanupInvalidLots };
