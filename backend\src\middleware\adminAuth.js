/**
 * Middleware d'authentification spécifique pour les administrateurs
 *
 * Ce middleware vérifie que l'utilisateur est bien un administrateur
 * et que son compte est validé et non bloqué.
 */

const jwt = require('jsonwebtoken');
const Admin = require('../models/Admin');
const { NotFoundError, ForbiddenError, UnauthorizedError } = require('./errorHandler');

/**
 * Middleware pour vérifier si l'utilisateur est un administrateur
 *
 * @param {Object} req - L'objet request Express
 * @param {Object} res - L'objet response Express
 * @param {Function} next - La fonction middleware suivante
 */
const isAdmin = async (req, res, next) => {
  try {
    // Vérifier si le token existe dans les headers
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.error('Authentification requise', 401);
    }

    const token = authHeader.split(' ')[1];

    // Vérifier et décoder le token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      return res.error('Token invalide ou expiré', 401);
    }

    // Vérifier si l'utilisateur a le rôle admin
    if (!decoded.roles || !decoded.roles.includes('ROLE_ADMIN')) {
      return res.error('Accès réservé aux administrateurs', 403);
    }

    // Récupérer l'administrateur depuis la base de données
    const admin = await Admin.findById(decoded._id);
    if (!admin) {
      return res.error('Administrateur non trouvé', 404);
    }

    // Vérifier si le compte est validé et non bloqué
    if (!admin.isValidated) {
      return res.error('Votre compte est en attente de validation', 403);
    }

    if (admin.isBlocked) {
      return res.error('Votre compte a été bloqué', 403);
    }

    // Ajouter l'administrateur à l'objet request
    req.admin = admin;
    req.user = admin; // Pour la compatibilité avec les autres middlewares

    // Journaliser l'accès administrateur
    console.log(`[${new Date().toISOString()}] ADMIN ACCESS: ${admin.email} (${admin._id})`);

    next();
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ADMIN AUTH ERROR: ${error.message}`);
    return res.error('Erreur d\'authentification', 500);
  }
};

module.exports = { isAdmin };