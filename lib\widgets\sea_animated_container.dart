import 'package:flutter/material.dart';

/// Widget de conteneur animé pour l'application SeaTrace
class SeaAnimatedContainer extends StatefulWidget {
  /// Le contenu à afficher dans le conteneur
  final Widget child;

  /// La durée de l'animation
  final Duration duration;

  /// Le délai avant le début de l'animation
  final Duration delay;

  /// Le type d'animation
  final AnimationType type;

  /// La courbe d'animation
  final Curve curve;

  /// Crée un nouveau conteneur animé.
  const SeaAnimatedContainer({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.delay = Duration.zero,
    this.type = AnimationType.fadeIn,
    this.curve = Curves.easeOutCubic,
  });

  @override
  State<SeaAnimatedContainer> createState() => _SeaAnimatedContainerState();
}

class _SeaAnimatedContainerState extends State<SeaAnimatedContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.type) {
      case AnimationType.fadeIn:
        return FadeTransition(
          opacity: _animation,
          child: widget.child,
        );
      case AnimationType.slideUp:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.2),
            end: Offset.zero,
          ).animate(_animation),
          child: FadeTransition(
            opacity: _animation,
            child: widget.child,
          ),
        );
      case AnimationType.slideDown:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, -0.2),
            end: Offset.zero,
          ).animate(_animation),
          child: FadeTransition(
            opacity: _animation,
            child: widget.child,
          ),
        );
      case AnimationType.slideLeft:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(_animation),
          child: FadeTransition(
            opacity: _animation,
            child: widget.child,
          ),
        );
      case AnimationType.slideRight:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-0.2, 0),
            end: Offset.zero,
          ).animate(_animation),
          child: FadeTransition(
            opacity: _animation,
            child: widget.child,
          ),
        );
      case AnimationType.scale:
        return ScaleTransition(
          scale: _animation,
          child: FadeTransition(
            opacity: _animation,
            child: widget.child,
          ),
        );
    }
  }
}

/// Types d'animations disponibles
enum AnimationType {
  /// Fondu entrant
  fadeIn,

  /// Glissement vers le haut
  slideUp,

  /// Glissement vers le bas
  slideDown,

  /// Glissement vers la gauche
  slideLeft,

  /// Glissement vers la droite
  slideRight,

  /// Mise à l'échelle
  scale,
}
