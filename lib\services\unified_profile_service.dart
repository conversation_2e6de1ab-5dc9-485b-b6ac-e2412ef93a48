import 'dart:io';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/client.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/utils/error_handler.dart';

/// Service unifié pour gérer les profils utilisateurs
class UnifiedProfileService {
  // Singleton
  static final UnifiedProfileService _instance =
      UnifiedProfileService._internal();
  factory UnifiedProfileService() => _instance;
  UnifiedProfileService._internal();

  // Services
  final _apiService = UnifiedApiService();
  final _authService = UnifiedAuthService();

  /// Récupérer l'utilisateur actuel
  Future<dynamic> getCurrentUser() async {
    try {
      return await _authService.getCurrentUser();
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService.getCurrentUser',
      );
      return null;
    }
  }

  /// Mettre à jour le profil utilisateur
  Future<bool> updateProfile(dynamic user) async {
    try {
      // Si user est un Map, mettre à jour l'utilisateur actuel
      if (user is Map<String, dynamic>) {
        final currentUser = await _authService.getCurrentUser();
        if (currentUser == null) {
          throw Exception('Utilisateur non connecté');
        }

        // Log pour le débogage
        ErrorHandler.instance.logInfo(
          'Mise à jour du profil avec les données: $user',
          context: 'UnifiedProfileService.updateProfile',
        );

        if (currentUser is Pecheur) {
          // Créer un nouvel objet Pecheur avec les données mises à jour
          final updatedPecheur = Pecheur(
            id: currentUser.id,
            nom: user['nom'] ?? currentUser.nom,
            prenom: user['prenom'] ?? currentUser.prenom,
            email: currentUser.email,
            password: currentUser.password,
            telephone: user['telephone'] ?? currentUser.telephone,
            photo: user['photo'] ?? currentUser.photo,
            roles: currentUser.roles,
          );
          return await _updatePecheur(updatedPecheur);
        } else if (currentUser is Veterinaire) {
          // Créer un nouvel objet Veterinaire avec les données mises à jour
          final updatedVeterinaire = Veterinaire(
            id: currentUser.id,
            nom: user['nom'] ?? currentUser.nom,
            prenom: user['prenom'] ?? currentUser.prenom,
            email: currentUser.email,
            password: currentUser.password,
            telephone: user['telephone'] ?? currentUser.telephone,
            photo: user['photo'] ?? currentUser.photo,
            roles: currentUser.roles,
          );
          return await _updateVeterinaire(updatedVeterinaire);
        } else if (currentUser is Maryeur) {
          // Créer un nouvel objet Maryeur avec les données mises à jour
          final updatedMaryeur = Maryeur(
            id: currentUser.id,
            nom: user['nom'] ?? currentUser.nom,
            prenom: user['prenom'] ?? currentUser.prenom,
            email: currentUser.email,
            password: currentUser.password,
            telephone: user['telephone'] ?? currentUser.telephone,
            photo: user['photo'] ?? currentUser.photo,
            roles: currentUser.roles,
          );
          return await _updateMaryeur(updatedMaryeur);
        } else if (currentUser is Client) {
          // Créer un nouvel objet Client avec les données mises à jour
          final updatedClient = Client(
            id: currentUser.id,
            nom: user['nom'] ?? currentUser.nom,
            prenom: user['prenom'] ?? currentUser.prenom,
            email: currentUser.email,
            password: currentUser.password,
            telephone: user['telephone'] ?? currentUser.telephone,
            photo: user['photo'] ?? currentUser.photo,
            roles: currentUser.roles,
          );
          return await _updateClient(updatedClient);
        } else {
          throw ArgumentError('Type d\'utilisateur non pris en charge');
        }
      } else if (user is Pecheur) {
        return await _updatePecheur(user);
      } else if (user is Veterinaire) {
        return await _updateVeterinaire(user);
      } else if (user is Maryeur) {
        return await _updateMaryeur(user);
      } else if (user is Client) {
        return await _updateClient(user);
      } else {
        throw ArgumentError('Type d\'utilisateur non pris en charge');
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService.updateProfile',
      );
      return false;
    }
  }

  /// Mettre à jour le profil d'un pêcheur
  Future<bool> _updatePecheur(Pecheur pecheur) async {
    try {
      // Créer une map avec seulement les champs à mettre à jour
      // Ne pas inclure le mot de passe dans la mise à jour
      final Map<String, dynamic> updateData = {
        'nom': pecheur.nom,
        'prenom': pecheur.prenom,
        'telephone': pecheur.telephone,
        'photo': pecheur.photo,
      };

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Mise à jour du pêcheur ${pecheur.id} avec les données: $updateData',
        context: 'UnifiedProfileService._updatePecheur',
      );

      final response = await _apiService.put(
        'pecheurs/${pecheur.id}',
        updateData,
      );

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse du serveur: $response',
        context: 'UnifiedProfileService._updatePecheur',
      );

      return response['success'] == true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService._updatePecheur',
      );
      return false;
    }
  }

  /// Mettre à jour le profil d'un vétérinaire
  Future<bool> _updateVeterinaire(Veterinaire veterinaire) async {
    try {
      // Créer une map avec seulement les champs à mettre à jour
      // Ne pas inclure le mot de passe dans la mise à jour
      final Map<String, dynamic> updateData = {
        'nom': veterinaire.nom,
        'prenom': veterinaire.prenom,
        'telephone': veterinaire.telephone,
        'photo': veterinaire.photo,
      };

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Mise à jour du vétérinaire ${veterinaire.id} avec les données: $updateData',
        context: 'UnifiedProfileService._updateVeterinaire',
      );

      final response = await _apiService.put(
        'veterinaires/${veterinaire.id}',
        updateData,
      );

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse du serveur: $response',
        context: 'UnifiedProfileService._updateVeterinaire',
      );

      return response['success'] == true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService._updateVeterinaire',
      );
      return false;
    }
  }

  /// Mettre à jour le profil d'un maryeur
  Future<bool> _updateMaryeur(Maryeur maryeur) async {
    try {
      // Créer une map avec seulement les champs à mettre à jour
      // Ne pas inclure le mot de passe dans la mise à jour
      final Map<String, dynamic> updateData = {
        'nom': maryeur.nom,
        'prenom': maryeur.prenom,
        'telephone': maryeur.telephone,
        'photo': maryeur.photo,
      };

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Mise à jour du maryeur ${maryeur.id} avec les données: $updateData',
        context: 'UnifiedProfileService._updateMaryeur',
      );

      final response = await _apiService.put(
        'maryeurs/${maryeur.id}',
        updateData,
      );

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse du serveur: $response',
        context: 'UnifiedProfileService._updateMaryeur',
      );

      return response['success'] == true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService._updateMaryeur',
      );
      return false;
    }
  }

  /// Mettre à jour le profil d'un client
  Future<bool> _updateClient(Client client) async {
    try {
      // Créer une map avec seulement les champs à mettre à jour
      // Ne pas inclure le mot de passe dans la mise à jour
      final Map<String, dynamic> updateData = {
        'nom': client.nom,
        'prenom': client.prenom,
        'telephone': client.telephone,
        'photo': client.photo,
      };

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Mise à jour du client ${client.id} avec les données: $updateData',
        context: 'UnifiedProfileService._updateClient',
      );

      final response = await _apiService.put(
        'clients/${client.id}',
        updateData,
      );

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse du serveur: $response',
        context: 'UnifiedProfileService._updateClient',
      );

      return response['success'] == true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService._updateClient',
      );
      return false;
    }
  }

  /// Changer le mot de passe
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      final user = await _authService.getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      final response = await _apiService.put('auth/change-password', {
        'currentPassword': oldPassword,
        'newPassword': newPassword,
      });

      return response['success'] == true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService.changePassword',
      );
      return false;
    }
  }

  /// Télécharger une photo de profil
  Future<String?> uploadProfilePhoto(String filePath) async {
    try {
      final user = await _authService.getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Vérifier que le fichier existe
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Le fichier n\'existe pas: $filePath');
      }

      // Vérifier la taille du fichier
      final fileSize = await file.length();
      if (fileSize > 5 * 1024 * 1024) {
        // 5 MB
        throw Exception('La taille du fichier dépasse la limite de 5 MB');
      }

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Téléchargement de la photo de profil: $filePath (${fileSize / 1024} KB)',
        context: 'UnifiedProfileService.uploadProfilePhoto',
      );

      // Utiliser l'endpoint correct pour l'upload d'images
      final response = await _apiService.uploadFile('images/upload', filePath);

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse du serveur: $response',
        context: 'UnifiedProfileService.uploadProfilePhoto',
      );

      // Le résultat est directement le chemin du fichier
      return response;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedProfileService.uploadProfilePhoto',
      );
      return null;
    }
  }

  /// Obtenir le type d'utilisateur
  String getUserType(dynamic user) {
    if (user is Pecheur) {
      return 'pecheur';
    } else if (user is Veterinaire) {
      return 'veterinaire';
    } else if (user is Maryeur) {
      return 'maryeur';
    } else if (user is Client) {
      return 'client';
    } else {
      return 'unknown';
    }
  }
}
