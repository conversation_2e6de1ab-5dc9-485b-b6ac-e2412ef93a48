import 'package:flutter/material.dart';

/// Widget réutilisable pour afficher le logo SeaTrace
/// 
/// Ce widget affiche le logo personnalisé de l'application avec
/// un fallback vers une icône par défaut en cas d'erreur.
class SeaLogo extends StatelessWidget {
  /// Taille du logo
  final double size;
  
  /// Couleur de l'icône de fallback
  final Color? fallbackColor;
  
  /// Si true, affiche le logo dans un cercle avec un arrière-plan
  final bool withBackground;
  
  /// Couleur de l'arrière-plan (si withBackground est true)
  final Color? backgroundColor;
  
  /// Padding autour du logo (si withBackground est true)
  final double padding;

  const SeaLogo({
    super.key,
    this.size = 64,
    this.fallbackColor,
    this.withBackground = false,
    this.backgroundColor,
    this.padding = 16,
  });

  /// Constructeur pour un logo avec arrière-plan circulaire
  const SeaLogo.withCircularBackground({
    super.key,
    this.size = 64,
    this.fallbackColor,
    this.backgroundColor,
    this.padding = 16,
  }) : withBackground = true;

  /// Constructeur pour un logo simple sans arrière-plan
  const SeaLogo.simple({
    super.key,
    this.size = 64,
    this.fallbackColor,
    this.padding = 0,
  }) : withBackground = false, backgroundColor = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveFallbackColor = fallbackColor ?? theme.primaryColor;
    final effectiveBackgroundColor = backgroundColor ?? 
        effectiveFallbackColor.withValues(alpha: 0.1);

    Widget logoWidget = ClipOval(
      child: Image.asset(
        'lib/assets/images/logo.jpeg',
        width: size,
        height: size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // Fallback vers l'icône par défaut en cas d'erreur
          return Icon(
            Icons.sailing,
            size: size,
            color: effectiveFallbackColor,
          );
        },
      ),
    );

    if (withBackground) {
      return Container(
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          color: effectiveBackgroundColor,
          shape: BoxShape.circle,
        ),
        child: logoWidget,
      );
    }

    return logoWidget;
  }
}

/// Widget pour afficher le logo avec le nom de l'application
class SeaLogoWithTitle extends StatelessWidget {
  /// Taille du logo
  final double logoSize;
  
  /// Style du texte du titre
  final TextStyle? titleStyle;
  
  /// Espacement entre le logo et le titre
  final double spacing;
  
  /// Si true, affiche le logo avec un arrière-plan circulaire
  final bool withBackground;
  
  /// Couleur de l'arrière-plan du logo
  final Color? backgroundColor;

  const SeaLogoWithTitle({
    super.key,
    this.logoSize = 64,
    this.titleStyle,
    this.spacing = 16,
    this.withBackground = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTitleStyle = titleStyle ?? 
        theme.textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.primaryColor,
          letterSpacing: 1.2,
        );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        withBackground
            ? SeaLogo.withCircularBackground(
                size: logoSize,
                backgroundColor: backgroundColor,
              )
            : SeaLogo.simple(
                size: logoSize,
              ),
        SizedBox(height: spacing),
        Text(
          'SeaTrace',
          style: effectiveTitleStyle,
        ),
      ],
    );
  }
}
