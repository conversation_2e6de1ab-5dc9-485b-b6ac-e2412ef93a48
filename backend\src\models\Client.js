/**
 * <PERSON><PERSON><PERSON><PERSON> Client
 * Représente un client dans l'application SeaTrace
 */

const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');

const clientSchema = new mongoose.Schema({
  // Informations d'identification
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: [validator.isEmail, 'Email invalide']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  roles: {
    type: String,
    required: true,
    default: 'ROLE_CLIENT'
  },

  // Informations personnelles
  nom: {
    type: String,
    required: true,
    trim: true
  },
  prenom: {
    type: String,
    required: true,
    trim: true
  },
  telephone: String,
  photo: String,

  // Informations de contact
  adresse: String,

  // Statut du compte
  isValidated: {
    type: Boolean,
    default: false
  },
  isBlocked: {
    type: Boolean,
    default: false
  },

  // Relations
  achats: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lot'
  }]
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(_, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

/**
 * Middleware: Hash du mot de passe avant sauvegarde
 */
clientSchema.pre('save', async function(next) {
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 8);
  }
  next();
});

/**
 * Méthode: Vérification du mot de passe
 */
clientSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

/**
 * Méthode: Obtention du type d'utilisateur
 */
clientSchema.methods.getUserType = function() {
  return 'client';
};

const Client = mongoose.model('Client', clientSchema);

module.exports = Client;
