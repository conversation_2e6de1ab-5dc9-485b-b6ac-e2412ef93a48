import 'package:flutter/foundation.dart';

/// Configuration des services de l'application
class ServiceConfig {
  /// Instance singleton
  static final ServiceConfig _instance = ServiceConfig._internal();

  /// Constructeur factory pour accéder à l'instance singleton
  factory ServiceConfig() => _instance;

  /// Constructeur privé pour l'initialisation
  ServiceConfig._internal();

  /// URL de base de l'API
  String get apiBaseUrl {
    if (kDebugMode) {
      // En mode développement, utiliser l'adresse IP de la machine
      // qui fonctionne avec l'émulateur Android
    return 'http://*************:3005/api'; // return 'http://***********:3005/api'; // IP locale de la machine
    } else {
      // URL de production
      return 'https://api.seatrace.com/api';
    }
  }

  /// Liste des adresses IP à tester pour le développement
  static List<String> get developmentIPs => [
    '***********', // IP locale de la machine (priorité)
    '********', // Émulateur Android standard
    '***********', // IP locale alternative
    '***********', // Passerelle réseau
    '127.0.0.1', // Localhost
    '*************',
    'localhost', // Localhost par nom
  ];

  /// Retourne l'URL de base pour les services
  static String getBaseUrl() {
    if (kDebugMode) {
      // Utiliser l'adresse IP de la machine pour l'émulateur Android
   return 'http://*************:3005/api';//  return 'http://***********:3005'; 
    } else {
      return 'https://api.seatrace.com';
    }
  }

  /// WebSocket URL
  static String getWsUrl() {
    final baseUrl = getBaseUrl();
    return baseUrl
        .replaceAll('http://', 'ws://')
        .replaceAll('https://', 'wss://');
  }

  /// Default timeout for API requests (in seconds)
  int get defaultTimeout => 30;

  /// Maximum number of retries for API requests
  int get maxRetries => 3;

  /// Delay between retries (in milliseconds)
  int get retryDelay => 1000;

  /// Path to the fish classification model
  String get fishClassificationModelPath =>
      'assets/models/fish_classification_model.tflite';

  /// Notification refresh interval (in seconds)
  int get notificationRefreshInterval => 30;

  /// Maximum image size (in pixels)
  int get maxImageSize => 1024;

  /// Image compression quality (0-100)
  int get imageCompressionQuality => 85;

  /// Token expiration duration (in days)
  int get tokenExpirationDays => 30;

  /// Maximum items per page for pagination
  int get paginationLimit => 20;

  /// Whether debug logs are enabled
  bool get enableDebugLogs => kDebugMode;

  /// Whether offline mode is enabled
  bool get enableOfflineMode => true;

  /// Whether notifications are enabled
  bool get enableNotifications => true;

  /// Whether test mode is enabled
  bool get enableTestMode => false;

  /// Whether to use test data
  bool get useTestData => false;

  /// Currency used in the application
  String get currency => 'TND';

  /// Currency symbol
  String get currencySymbol => 'د.ت';

  /// Default date format
  String get defaultDateFormat => 'dd/MM/yyyy';

  /// Default time format
  String get defaultTimeFormat => 'HH:mm';

  /// Default date-time format
  String get defaultDateTimeFormat => 'dd/MM/yyyy HH:mm';

  /// Default locale
  String get defaultLocale => 'fr_TN';

  /// Default theme
  String get defaultTheme => 'light';

  /// Primary color of the application
  int get primaryColor => 0xFF0277BD; // Blue

  /// Secondary color of the application
  int get secondaryColor => 0xFF00796B; // Teal

  /// Accent color of the application
  int get accentColor => 0xFFFFB300; // Amber
}
