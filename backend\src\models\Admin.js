/**
 * <PERSON><PERSON><PERSON><PERSON> Admin
 * Représente un administrateur dans l'application SeaTrace
 */

const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');

const adminSchema = new mongoose.Schema({
  // Informations d'identification
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: [validator.isEmail, 'Email invalide']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  roles: {
    type: String,
    required: true,
    default: 'ROLE_ADMIN'
  },

  // Informations personnelles
  nom: {
    type: String,
    required: true,
    trim: true
  },
  prenom: {
    type: String,
    required: true,
    trim: true
  },
  telephone: String,
  photo: String,

  // Statut du compte
  isValidated: {
    type: Boolean,
    default: true
  },
  isBlocked: {
    type: Boolean,
    default: false
  },

  // Informations supplémentaires
  permissions: String,
  lastLogin: Date
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(_, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

/**
 * Middleware: Hash du mot de passe avant sauvegarde
 */
adminSchema.pre('save', async function(next) {
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 8);
  }
  next();
});

/**
 * Méthode: Vérification du mot de passe
 */
adminSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

/**
 * Méthode: Vérification de rôle
 */
adminSchema.methods.hasRole = function(role) {
  return this.roles.includes(role);
};

/**
 * Méthode: Obtention du type d'utilisateur
 */
adminSchema.methods.getUserType = function() {
  return 'admin';
};

const Admin = mongoose.model('Admin', adminSchema);

module.exports = Admin;
