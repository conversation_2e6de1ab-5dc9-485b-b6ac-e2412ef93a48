import 'package:flutter/material.dart';

import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';

class SystemSettingsScreen extends StatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  State<SystemSettingsScreen> createState() => _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends State<SystemSettingsScreen> {
  final _responsiveService = ResponsiveService();

  bool _isLoading = true;
  String? _errorMessage;

  // Paramètres du système
  bool _enableNotifications = true;
  bool _enableAIClassification = true;
  bool _enableRealTimeAuctions = true;
  bool _enableDarkMode = false;
  String _defaultCurrency = 'DZD';
  String _defaultLanguage = 'fr';
  double _minBidIncrement = 100.0;
  int _auctionDuration = 24;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Dans une version réelle, ces données viendraient de l'API
      // Pour le moment, nous utilisons des données simulées

      // Simuler un délai de chargement
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        // Valeurs par défaut
        _enableNotifications = true;
        _enableAIClassification = true;
        _enableRealTimeAuctions = true;
        _enableDarkMode = false;
        _defaultCurrency = 'DZD';
        _defaultLanguage = 'fr';
        _minBidIncrement = 100.0;
        _auctionDuration = 24;

        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'SystemSettingsScreen._loadSettings',
      );
      setState(() {
        _errorMessage =
            'Erreur lors du chargement des paramètres: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Dans une version réelle, ces données seraient envoyées à l'API
      // Pour le moment, nous simulons juste un délai

      // Simuler un délai de sauvegarde
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Paramètres enregistrés avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'SystemSettingsScreen._saveSettings',
      );
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de l\'enregistrement: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('Erreur', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Une erreur inconnue est survenue',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSettings,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres système'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSettings,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? _buildErrorView()
                : SingleChildScrollView(
                  padding: _responsiveService.adaptivePadding(context),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Paramètres généraux
                        SeaSectionHeader(
                          title: 'Paramètres généraux',
                          icon: Icons.settings,
                        ),
                        SeaCard(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Devise par défaut
                                Text(
                                  'Devise par défaut',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                DropdownButtonFormField<String>(
                                  value: _defaultCurrency,
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  items: const [
                                    DropdownMenuItem(
                                      value: 'DZD',
                                      child: Text('Dinar algérien (DZD)'),
                                    ),
                                    DropdownMenuItem(
                                      value: 'EUR',
                                      child: Text('Euro (EUR)'),
                                    ),
                                    DropdownMenuItem(
                                      value: 'USD',
                                      child: Text('Dollar américain (USD)'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _defaultCurrency = value!;
                                    });
                                  },
                                ),

                                const SizedBox(height: 16),

                                // Langue par défaut
                                Text(
                                  'Langue par défaut',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                DropdownButtonFormField<String>(
                                  value: _defaultLanguage,
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  items: const [
                                    DropdownMenuItem(
                                      value: 'fr',
                                      child: Text('Français'),
                                    ),
                                    DropdownMenuItem(
                                      value: 'ar',
                                      child: Text('العربية (Arabe)'),
                                    ),
                                    DropdownMenuItem(
                                      value: 'en',
                                      child: Text('English (Anglais)'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _defaultLanguage = value!;
                                    });
                                  },
                                ),

                                const SizedBox(height: 16),

                                // Thème sombre
                                SwitchListTile(
                                  title: Text(
                                    'Thème sombre par défaut',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  value: _enableDarkMode,
                                  onChanged: (value) {
                                    setState(() {
                                      _enableDarkMode = value;
                                    });
                                  },
                                  secondary: Icon(
                                    _enableDarkMode
                                        ? Icons.dark_mode
                                        : Icons.light_mode,
                                    color:
                                        _enableDarkMode
                                            ? Colors.amber
                                            : Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Paramètres des enchères
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Paramètres des enchères',
                          icon: Icons.gavel,
                        ),
                        SeaCard(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Incrément minimal des enchères
                                Text(
                                  'Incrément minimal des enchères (DZD)',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextFormField(
                                  initialValue: _minBidIncrement.toString(),
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                    prefixIcon: Icon(Icons.monetization_on),
                                  ),
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Veuillez entrer une valeur';
                                    }
                                    final number = double.tryParse(value);
                                    if (number == null || number <= 0) {
                                      return 'Veuillez entrer un nombre positif';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    final number = double.tryParse(value);
                                    if (number != null) {
                                      setState(() {
                                        _minBidIncrement = number;
                                      });
                                    }
                                  },
                                ),

                                const SizedBox(height: 16),

                                // Durée des enchères
                                Text(
                                  'Durée par défaut des enchères (heures)',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextFormField(
                                  initialValue: _auctionDuration.toString(),
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                    prefixIcon: Icon(Icons.timer),
                                  ),
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Veuillez entrer une valeur';
                                    }
                                    final number = int.tryParse(value);
                                    if (number == null || number <= 0) {
                                      return 'Veuillez entrer un nombre positif';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    final number = int.tryParse(value);
                                    if (number != null) {
                                      setState(() {
                                        _auctionDuration = number;
                                      });
                                    }
                                  },
                                ),

                                const SizedBox(height: 16),

                                // Enchères en temps réel
                                SwitchListTile(
                                  title: Text(
                                    'Activer les enchères en temps réel',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  subtitle: const Text(
                                    'Utilise les WebSockets pour les mises à jour instantanées',
                                  ),
                                  value: _enableRealTimeAuctions,
                                  onChanged: (value) {
                                    setState(() {
                                      _enableRealTimeAuctions = value;
                                    });
                                  },
                                  secondary: Icon(
                                    Icons.bolt,
                                    color:
                                        _enableRealTimeAuctions
                                            ? Colors.amber
                                            : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Paramètres des fonctionnalités
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Fonctionnalités',
                          icon: Icons.featured_play_list,
                        ),
                        SeaCard(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Notifications
                                SwitchListTile(
                                  title: Text(
                                    'Activer les notifications',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  value: _enableNotifications,
                                  onChanged: (value) {
                                    setState(() {
                                      _enableNotifications = value;
                                    });
                                  },
                                  secondary: Icon(
                                    Icons.notifications,
                                    color:
                                        _enableNotifications
                                            ? theme.primaryColor
                                            : Colors.grey,
                                  ),
                                ),

                                // Classification IA
                                SwitchListTile(
                                  title: Text(
                                    'Activer la classification IA',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  subtitle: const Text(
                                    'Utilise l\'intelligence artificielle pour identifier les espèces de poissons',
                                  ),
                                  value: _enableAIClassification,
                                  onChanged: (value) {
                                    setState(() {
                                      _enableAIClassification = value;
                                    });
                                  },
                                  secondary: Icon(
                                    Icons.auto_awesome,
                                    color:
                                        _enableAIClassification
                                            ? Colors.purple
                                            : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Bouton de sauvegarde
                        const SizedBox(height: 24),
                        Center(
                          child: SeaButton.primary(
                            text: 'Enregistrer les paramètres',
                            icon: Icons.save,
                            onPressed: _saveSettings,
                            width: 250,
                          ),
                        ),

                        // Espace en bas
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }
}
