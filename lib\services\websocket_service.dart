import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification.dart' as model;
import '../utils/error_handler.dart';
import 'service_config.dart';
import 'unified_auth_service.dart';

/// Service pour gérer les connexions WebSocket
class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  WebSocketChannel? _channel;
  bool _isConnected = false;

  // Stream controller pour les notifications
  final _notificationsController =
      StreamController<model.Notification>.broadcast();

  // Stream controller pour les mises à jour d'enchères
  final _auctionUpdatesController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _auctionStartController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _auctionEndController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _newBidController = StreamController<Map<String, dynamic>>.broadcast();

  // Getters
  Stream<model.Notification> get notifications =>
      _notificationsController.stream;
  Stream<Map<String, dynamic>> get auctionUpdates =>
      _auctionUpdatesController.stream;
  Stream<Map<String, dynamic>> get auctionStarts =>
      _auctionStartController.stream;
  Stream<Map<String, dynamic>> get auctionEnds => _auctionEndController.stream;
  Stream<Map<String, dynamic>> get newBids => _newBidController.stream;
  bool get isConnected => _isConnected;

  /// Initialiser la connexion WebSocket
  Future<void> initialize() async {
    try {
      // Récupérer le token d'authentification
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        debugPrint('WebSocketService: Pas de token disponible');
        return;
      }

      // Récupérer l'ID de l'utilisateur
      final authService = UnifiedAuthService();
      final user = await authService.getCurrentUser();

      if (user == null) {
        debugPrint('WebSocketService: Utilisateur non connecté');
        return;
      }

      // Construire l'URL WebSocket
      final baseUrl = ServiceConfig.getBaseUrl();

      // Extraire l'adresse IP et le port
      Uri uri = Uri.parse(baseUrl);
      String host = uri.host;
      int port = uri.port > 0 ? uri.port : 3005;

      // Construire l'URL WebSocket correctement
      final wsUrl = 'ws://$host:$port/ws?token=$token';

      // Journaliser l'URL pour le débogage
      debugPrint('WebSocketService: URL WebSocket: $wsUrl');

      // Fermer la connexion existante si elle existe
      await disconnect();

      try {
        // Ouvrir une nouvelle connexion avec un timeout
        _channel = IOWebSocketChannel.connect(
          wsUrl,
          pingInterval: Duration(seconds: 30),
          connectTimeout: Duration(seconds: 10),
        );
        _isConnected = true;

        // Écouter les messages
        _channel!.stream.listen(
          _onMessage,
          onError: _onError,
          onDone: _onDone,
          cancelOnError: false,
        );

        debugPrint('WebSocketService: Connexion établie');
      } catch (socketError) {
        _isConnected = false;
        ErrorHandler.instance.logError(
          socketError,
          context: 'WebSocketService.initialize.socketConnect',
        );
        debugPrint(
          'WebSocketService: Erreur de connexion WebSocket: $socketError',
        );

        // Ne pas relancer l'erreur, mais continuer sans WebSocket
        // L'application fonctionnera en mode polling
      }
    } catch (e) {
      _isConnected = false;
      ErrorHandler.instance.logError(e, context: 'WebSocketService.initialize');
      debugPrint('WebSocketService: Erreur lors de l\'initialisation: $e');
    }
  }

  /// Déconnecter le WebSocket
  Future<void> disconnect() async {
    if (_channel != null) {
      await _channel!.sink.close();
      _channel = null;
      _isConnected = false;
      debugPrint('WebSocketService: Déconnexion');
    }
  }

  /// Gérer les messages reçus
  void _onMessage(dynamic message) {
    try {
      final data = jsonDecode(message);
      debugPrint('WebSocketService: Message reçu: $data');

      switch (data['type']) {
        case 'notification':
          // Convertir en objet Notification
          final notification = model.Notification.fromMap(data['data']);
          _notificationsController.add(notification);
          break;

        case 'auction_update':
          // Récupérer les données de mise à jour d'enchère
          final auctionData = data['data'] as Map<String, dynamic>;
          _auctionUpdatesController.add(auctionData);
          debugPrint(
            'WebSocketService: Mise à jour d\'enchère reçue: $auctionData',
          );
          break;

        case 'auction_started':
          final auctionData = data['data'] as Map<String, dynamic>;
          _auctionStartController.add(auctionData);
          debugPrint(
            'WebSocketService: Nouvelle enchère démarrée: $auctionData',
          );
          break;

        case 'auction_ended':
          final auctionData = data['data'] as Map<String, dynamic>;
          _auctionEndController.add(auctionData);
          debugPrint('WebSocketService: Enchère terminée: $auctionData');
          break;

        case 'new_bid':
          final bidData = data['data'] as Map<String, dynamic>;
          _newBidController.add(bidData);
          debugPrint('WebSocketService: Nouvelle enchère: $bidData');
          break;

        default:
          debugPrint(
            'WebSocketService: Type de message non géré: ${data['type']}',
          );
      }
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'WebSocketService._onMessage');
      debugPrint('WebSocketService: Erreur lors du traitement du message: $e');
    }
  }

  /// Gérer les erreurs
  void _onError(error) {
    _isConnected = false;
    ErrorHandler.instance.logError(error, context: 'WebSocketService._onError');
    debugPrint('WebSocketService: Erreur: $error');

    // Tenter de se reconnecter après un délai
    Future.delayed(const Duration(seconds: 5), () {
      if (!_isConnected) {
        initialize();
      }
    });
  }

  /// Gérer la fermeture de la connexion
  void _onDone() {
    _isConnected = false;
    debugPrint('WebSocketService: Connexion fermée');

    // Tenter de se reconnecter après un délai
    Future.delayed(const Duration(seconds: 5), () {
      if (!_isConnected) {
        initialize();
      }
    });
  }

  /// Envoyer un message
  void send(Map<String, dynamic> data) {
    if (_isConnected && _channel != null) {
      _channel!.sink.add(jsonEncode(data));
    }
  }

  /// Rejoindre une salle d'enchère
  void joinAuction(String lotId) {
    send({'type': 'join_auction', 'lotId': lotId});
    debugPrint('WebSocketService: Rejoint l\'enchère $lotId');
  }

  /// Quitter une salle d'enchère
  void leaveAuction(String lotId) {
    send({'type': 'leave_auction', 'lotId': lotId});
    debugPrint('WebSocketService: Quitté l\'enchère $lotId');
  }

  /// Disposer des ressources
  void dispose() {
    disconnect();
    _notificationsController.close();
    _auctionUpdatesController.close();
    _auctionStartController.close();
    _auctionEndController.close();
    _newBidController.close();
  }
}
