import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/services/unified_notification_service.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/screens/login_screen.dart';
import 'package:seatrace/screens/pecheur/scan_fish_screen.dart';
import 'package:seatrace/screens/pecheur/history_screen.dart';
import 'package:seatrace/screens/profile_screen_new.dart';
import 'package:seatrace/screens/notifications_screen.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/utils/navigation_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/widgets/sea_widgets.dart';

class PecheurDashboardScreen extends StatefulWidget {
  const PecheurDashboardScreen({super.key});

  @override
  State<PecheurDashboardScreen> createState() => _PecheurDashboardScreenState();
}

class _PecheurDashboardScreenState extends State<PecheurDashboardScreen> {
  String _userName = '';
  String _userPhoto = '';
  String _userTelephone = '';
  String _userBateau = '';
  String _userPort = '';
  String _userMatricule = '';
  int _totalCaptures = 0;
  int _pendingValidation = 0;
  int _validated = 0;
  int _rejected = 0;
  bool _isLoading = true;
  String? _errorMessage;
  List<Map<String, dynamic>> _recentCaptures = [];

  final _animationService = AnimationService();
  final _responsiveService = ResponsiveService();
  final _navigationService = NavigationService();

  @override
  void initState() {
    super.initState();
    _loadUserData();

    // Initialiser le service de notifications
    UnifiedNotificationService().initialize();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Récupérer l'utilisateur connecté
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Vérifier que l'utilisateur est bien un pêcheur
      if (user is! Pecheur) {
        throw Exception('L\'utilisateur n\'est pas un pêcheur');
      }

      // Log pour déboguer
      ErrorHandler.instance.logInfo(
        'Utilisateur récupéré: id=${user.id}, nom=${user.nom}, prenom=${user.prenom}',
        context: 'PecheurDashboardScreen._loadUserData',
      );

      // Charger les détails du pêcheur
      final pecheur = await UnifiedApiService().getPecheur(user.id ?? '');
      final userData = pecheur?.toMap() ?? {};

      // Log pour déboguer
      ErrorHandler.instance.logInfo(
        'Détails pêcheur: ${userData.toString()}',
        context: 'PecheurDashboardScreen._loadUserData',
      );

      // Créer un objet utilisateur complet en combinant les données de base et les détails
      String userName = '${user.prenom} ${user.nom}';
      String userPhoto = user.photo ?? '';
      String userTelephone = user.telephone ?? '';
      String userBateau = '';
      String userPort = '';
      String userMatricule = '';

      // Mettre à jour les informations de base si elles sont disponibles dans les détails
      if (userData['prenom'] != null &&
          userData['prenom'].toString().isNotEmpty &&
          userData['nom'] != null &&
          userData['nom'].toString().isNotEmpty) {
        userName = '${userData['prenom']} ${userData['nom']}';
      }

      if (userData['photo'] != null &&
          userData['photo'].toString().isNotEmpty) {
        userPhoto = userData['photo'];
      }

      if (userData['telephone'] != null &&
          userData['telephone'].toString().isNotEmpty) {
        userTelephone = userData['telephone'];
      }

      // Récupérer les informations spécifiques au pêcheur
      if (userData['bateau'] != null) {
        userBateau = userData['bateau'].toString();
      }

      if (userData['port'] != null) {
        userPort = userData['port'].toString();
      }

      if (userData['matricule'] != null) {
        userMatricule = userData['matricule'].toString();
      }

      // Mettre à jour l'état avec les informations complètes
      setState(() {
        _userName = userName;
        _userPhoto = userPhoto;
        _userTelephone = userTelephone;
        _userBateau = userBateau;
        _userPort = userPort;
        _userMatricule = userMatricule;
      });

      // Charger les statistiques
      final lots = await UnifiedLotService().getLotsByPecheur();

      // Calculer les statistiques
      int totalCaptures = lots.length;
      int pendingValidation =
          lots.where((lot) => !lot.isValidated && !lot.vendu).length;
      int validated = lots.where((lot) => lot.isValidated).length;
      int rejected = lots.where((lot) => !lot.isValidated && lot.vendu).length;

      final stats = {
        'totalCaptures': totalCaptures,
        'pendingValidation': pendingValidation,
        'validated': validated,
        'rejected': rejected,
      };

      setState(() {
        _totalCaptures = stats['totalCaptures'] ?? 0;
        _pendingValidation = stats['pendingValidation'] ?? 0;
        _validated = stats['validated'] ?? 0;
        _rejected = stats['rejected'] ?? 0;
        _isLoading = false;
      });

      // Charger les captures récentes
      _loadRecentCaptures(user.id ?? '');
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'PecheurDashboardScreen._loadUserData',
      );

      // En cas d'erreur, essayer de récupérer au moins les informations de base de l'utilisateur
      try {
        final user = await UnifiedAuthService().getCurrentUser();
        if (user is Pecheur) {
          setState(() {
            _userName = '${user.prenom} ${user.nom}';
            _userPhoto = user.photo ?? '';
            _userTelephone = user.telephone ?? '';
            _isLoading = false;
          });
        }
      } catch (secondError) {
        debugPrint('Erreur secondaire: $secondError');
      }

      setState(() {
        _errorMessage = 'Erreur lors du chargement: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadRecentCaptures(String userId) async {
    try {
      final lots = await UnifiedLotService().getLotsByPecheur();

      // Trier par date (plus récent en premier) et prendre les 3 premiers
      lots.sort((a, b) {
        final dateA = a.dateTest ?? DateTime(1900);
        final dateB = b.dateTest ?? DateTime(1900);
        return dateB.compareTo(dateA);
      });

      // Convertir en Map pour la compatibilité avec le code existant
      final recentLots = lots.take(3).map((lot) => lot.toMap()).toList();

      setState(() {
        _recentCaptures = recentLots;
      });
    } catch (e) {
      // Gérer l'erreur silencieusement
      ErrorHandler.instance.logError(
        e,
        context: 'PecheurDashboardScreen._loadRecentCaptures',
      );
    }
  }

  Future<void> _logout() async {
    await UnifiedAuthService().logout();
    if (!mounted) return;
    _navigationService.replaceAllWithFade(context, const LoginScreen());
  }

  String _getInitials() {
    if (_userName.isEmpty) return '?';

    final nameParts = _userName.split(' ');
    String initials = '';

    if (nameParts.isNotEmpty && nameParts[0].isNotEmpty) {
      initials += nameParts[0][0].toUpperCase();
    }

    if (nameParts.length > 1 && nameParts[1].isNotEmpty) {
      initials += nameParts[1][0].toUpperCase();
    }

    return initials;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // primaryColor n'est plus utilisé ici car le bouton flottant a été supprimé
    final isPhone = _responsiveService.isPhone(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord Pêcheur'),
        elevation: 0,
        actions: [
          // Badge de notification
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: NotificationBadge(
              icon: Icons.notifications,
              iconSize: 24,
              badgeColor: theme.colorScheme.error,
              onTap: () {
                _navigationService.navigateToWithSlideLeft(
                  context,
                  const NotificationsScreen(),
                );
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              _navigationService.navigateToWithFade(
                context,
                const ProfileScreenNew(),
              );
            },
            tooltip: 'Profil',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Déconnexion',
          ),
        ],
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? _buildErrorView()
                : RefreshIndicator(
                  onRefresh: _loadUserData,
                  child: SingleChildScrollView(
                    padding: _responsiveService.adaptivePadding(context),
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _animationService.staggeredList([
                        // En-tête avec informations utilisateur
                        _buildWelcomeCard(),

                        // Actions rapides
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Actions rapides',
                          icon: Icons.bolt,
                        ),
                        _buildQuickActions(isPhone),

                        // Statistiques
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Statistiques',
                          icon: Icons.bar_chart,
                        ),
                        _buildStatisticsRow(),

                        // Captures récentes
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Captures récentes',
                          icon: Icons.history,
                          actionText: 'Voir tout',
                          onActionPressed: () {
                            _navigationService.navigateToWithSlideLeft(
                              context,
                              const HistoryScreen(),
                            );
                          },
                        ),
                        _buildRecentCapturesList(),
                      ]),
                    ),
                  ),
                ),
      ),
      // Bouton flottant supprimé pour éviter la duplication avec le bouton "Scanner un poisson"
    );
  }

  Widget _buildErrorView() {
    return _animationService.fadeIn(
      Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              SeaButton.primary(
                text: 'Réessayer',
                icon: Icons.refresh,
                onPressed: _loadUserData,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return SeaCard(
      elevated: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Avatar de l'utilisateur
              SeaAvatar(
                imageUrl:
                    _userPhoto.isNotEmpty
                        ? '${UnifiedApiService().getBaseUrl()}uploads/$_userPhoto'
                        : null,
                initials: _getInitials(),
                size: 60,
                backgroundColor: primaryColor.withValues(alpha: 0.1),
                foregroundColor: primaryColor,
                bordered: true,
                borderColor: primaryColor,
              ),
              const SizedBox(width: 16),

              // Informations utilisateur principales
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bienvenue, $_userName',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_userMatricule.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.badge,
                              size: 14,
                              color: theme.colorScheme.secondary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Matricule: $_userMatricule',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.secondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          // Informations supplémentaires
          const SizedBox(height: 12),
          const Divider(),
          const SizedBox(height: 8),

          // Informations du bateau et port
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_userBateau.isNotEmpty)
                      Row(
                        children: [
                          Icon(
                            Icons.directions_boat,
                            size: 16,
                            color: theme.hintColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Bateau: $_userBateau',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    const SizedBox(height: 4),
                    if (_userPort.isNotEmpty)
                      Row(
                        children: [
                          Icon(Icons.anchor, size: 16, color: theme.hintColor),
                          const SizedBox(width: 4),
                          Text(
                            'Port: $_userPort',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              // Téléphone
              if (_userTelephone.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.phone, size: 14, color: primaryColor),
                      const SizedBox(width: 4),
                      Text(
                        _userTelephone,
                        style: TextStyle(
                          color: primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(bool isPhone) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    if (isPhone) {
      return Column(
        children: [
          _buildActionCard(
            icon: Icons.camera_alt,
            title: 'Scanner un poisson',
            description: 'Identifier et enregistrer une nouvelle capture',
            color: primaryColor,
            onTap: () {
              _navigationService.navigateToWithSlideUp(
                context,
                const ScanFishScreen(),
              );
            },
          ),
          const SizedBox(height: 16),
          _buildActionCard(
            icon: Icons.history,
            title: 'Historique',
            description: 'Consulter vos captures précédentes',
            color: theme.colorScheme.secondary,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const HistoryScreen(),
              );
            },
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: _buildActionCard(
              icon: Icons.camera_alt,
              title: 'Scanner un poisson',
              description: 'Identifier et enregistrer une nouvelle capture',
              color: primaryColor,
              onTap: () {
                _navigationService.navigateToWithSlideUp(
                  context,
                  const ScanFishScreen(),
                );
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildActionCard(
              icon: Icons.history,
              title: 'Historique',
              description: 'Consulter vos captures précédentes',
              color: theme.colorScheme.secondary,
              onTap: () {
                _navigationService.navigateToWithSlideLeft(
                  context,
                  const HistoryScreen(),
                );
              },
            ),
          ),
        ],
      );
    }
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return SeaCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 28, color: color),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(description, style: theme.textTheme.bodyMedium),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Voir',
                    style: TextStyle(color: color, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 4),
                  Icon(Icons.arrow_forward, size: 16, color: color),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsRow() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          SeaStatCard(
            label: 'Captures totales',
            value: _totalCaptures.toString(),
            icon: Icons.catching_pokemon,
            color: theme.primaryColor,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const HistoryScreen(),
              );
            },
          ),
          const SizedBox(width: 12),
          SeaStatCard(
            label: 'En attente',
            value: _pendingValidation.toString(),
            icon: Icons.pending_actions,
            color: Colors.orange,
          ),
          const SizedBox(width: 12),
          SeaStatCard(
            label: 'Validées',
            value: _validated.toString(),
            icon: Icons.check_circle,
            color: theme.colorScheme.secondary,
          ),
          const SizedBox(width: 12),
          SeaStatCard(
            label: 'Refusées',
            value: _rejected.toString(),
            icon: Icons.cancel,
            color: theme.colorScheme.error,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentCapturesList() {
    final theme = Theme.of(context);

    if (_recentCaptures.isEmpty) {
      return _animationService.fadeIn(
        SeaCard(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Icon(
                    Icons.sailing_outlined,
                    size: 48,
                    color: theme.hintColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Aucune capture récente',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Scannez votre premier poisson pour commencer',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.hintColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  SeaButton.primary(
                    text: 'Scanner un poisson',
                    icon: Icons.camera_alt,
                    onPressed: () {
                      _navigationService.navigateToWithSlideUp(
                        context,
                        const ScanFishScreen(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return Column(
      children:
          _recentCaptures.map((capture) {
            final espece = capture['espece'] ?? 'Inconnu';
            final date =
                capture['datetest'] != null
                    ? DateTime.parse(
                      capture['datetest'],
                    ).toString().substring(0, 10)
                    : 'Date inconnue';
            final status =
                capture['test'] == 1
                    ? (capture['status'] == 1 ? 'Validé' : 'Refusé')
                    : 'En attente';

            IconData statusIcon;
            Color statusColor;

            switch (status) {
              case 'Validé':
                statusIcon = Icons.check_circle;
                statusColor = theme.colorScheme.secondary;
                break;
              case 'Refusé':
                statusIcon = Icons.cancel;
                statusColor = theme.colorScheme.error;
                break;
              default:
                statusIcon = Icons.pending_actions;
                statusColor = Colors.orange;
            }

            return _animationService.fadeIn(
              SeaCard(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(statusIcon, color: statusColor),
                  ),
                  title: Text(
                    espece,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 12,
                        color: theme.hintColor,
                      ),
                      const SizedBox(width: 4),
                      Text(date),
                      const SizedBox(width: 8),
                      Icon(Icons.scale, size: 12, color: theme.hintColor),
                      const SizedBox(width: 4),
                      Text('${capture['poid'] ?? 'N/A'} kg'),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  onTap: () {
                    // Naviguer vers les détails de la capture
                  },
                ),
              ),
            );
          }).toList(),
    );
  }
}
