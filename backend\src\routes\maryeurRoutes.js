/**
 * Routes pour les maryeurs (mareyeurs)
 */
const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Maryeur = require('../models/Maryeur');
const { auth, checkRole } = require('../middleware/auth');
const { NotFoundError, BadRequestError, ForbiddenError } = require('../middleware/errorHandler');

/**
 * @route GET /api/maryeurs
 * @desc Récupérer tous les mareyeurs actifs (validés et non bloqués)
 * @access Public
 */
router.get('/', async (req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Requête GET /api/maryeurs avec paramètres:`, req.query);

    // Par défaut, récupérer tous les mareyeurs validés
    let filter = { isValidated: true };

    // Filtrer par statut de validation si spécifié explicitement
    if (req.query.isValidated !== undefined) {
      // Convertir explicitement la chaîne 'true' en booléen true
      const isValidatedValue = req.query.isValidated === 'true';
      filter.isValidated = isValidatedValue;
      console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Paramètre isValidated spécifié: ${req.query.isValidated}, filtre appliqué: ${filter.isValidated}`);
    } else {
      console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Paramètre isValidated non spécifié, utilisation de la valeur par défaut: ${filter.isValidated}`);
    }

    // Ajouter le filtre de blocage si spécifié
    if (req.query.isBlocked !== undefined) {
      // Convertir explicitement la chaîne 'false' en booléen false
      const isBlockedValue = req.query.isBlocked === 'true';
      filter.isBlocked = isBlockedValue;
      console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Paramètre isBlocked spécifié: ${req.query.isBlocked}, filtre appliqué: ${filter.isBlocked}`);
    } else {
      // Par défaut, ne pas inclure les mareyeurs bloqués
      filter.isBlocked = false;
      console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Paramètre isBlocked non spécifié, utilisation de la valeur par défaut: ${filter.isBlocked}`);
    }

    // Si le paramètre all=true est fourni, récupérer tous les mareyeurs
    if (req.query.all === 'true') {
      // Supprimer les filtres de validation et de blocage
      filter = {};
    }

    console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Récupération des mareyeurs avec filtre:`, filter);

    // Récupérer tous les mareyeurs correspondant au filtre
    const maryeurs = await Maryeur.find(filter);

    // Ajouter des logs pour le débogage
    console.log(`[${new Date().toISOString()}] INFO [MARYEUR] ${maryeurs.length} mareyeurs trouvés avec le filtre appliqué`);

    if (maryeurs.length > 0) {
      // Afficher les détails de tous les mareyeurs trouvés
      maryeurs.forEach((m, index) => {
        console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Mareyeur #${index}:`, {
          id: m.id || m._id.toString(),
          nom: m.nom,
          prenom: m.prenom,
          email: m.email,
          isValidated: m.isValidated,
          isBlocked: m.isBlocked
        });
      });
    } else {
      // Vérifier s'il y a des mareyeurs dans la base de données
      const totalMaryeurs = await Maryeur.countDocuments({});
      console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Nombre total de mareyeurs dans la base: ${totalMaryeurs}`);

      // Si aucun mareyeur n'est trouvé avec le filtre mais qu'il y en a dans la base, afficher tous les mareyeurs pour le débogage
      if (totalMaryeurs > 0) {
        const allMaryeurs = await Maryeur.find({});
        console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Tous les mareyeurs dans la base:`);

        allMaryeurs.forEach((m, index) => {
          console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Mareyeur #${index}:`, {
            id: m.id || m._id.toString(),
            nom: m.nom,
            prenom: m.prenom,
            email: m.email,
            isValidated: m.isValidated,
            isBlocked: m.isBlocked
          });
        });

        // Vérifier s'il y a des mareyeurs validés
        const validatedMaryeurs = allMaryeurs.filter(m => m.isValidated === true);
        console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Nombre de mareyeurs validés: ${validatedMaryeurs.length}`);

        // Vérifier s'il y a des mareyeurs non bloqués
        const nonBlockedMaryeurs = allMaryeurs.filter(m => m.isBlocked !== true);
        console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Nombre de mareyeurs non bloqués: ${nonBlockedMaryeurs.length}`);

        // Vérifier s'il y a des mareyeurs validés et non bloqués
        const validAndNonBlockedMaryeurs = allMaryeurs.filter(m => m.isValidated === true && m.isBlocked !== true);
        console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Nombre de mareyeurs validés et non bloqués: ${validAndNonBlockedMaryeurs.length}`);
      }
    }

    // Transformer les données pour s'assurer que les champs id et isValidated sont présents
    const transformedMaryeurs = maryeurs.map(maryeur => {
      const maryeurObj = maryeur.toJSON();
      // S'assurer que l'ID est correctement défini
      if (!maryeurObj.id && maryeurObj._id) {
        maryeurObj.id = maryeurObj._id.toString();
      }
      return maryeurObj;
    });

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: transformedMaryeurs }, 'Liste des mareyeurs récupérée avec succès');
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ERROR [MARYEUR] Erreur lors de la récupération des mareyeurs:`, error);
    next(error);
  }
});

/**
 * @route POST /api/maryeurs
 * @desc Créer un nouveau mareyeur
 * @access Private (Admin)
 */
router.post('/', auth, checkRole('ROLE_ADMIN'), async (req, res, next) => {
  try {
    // Assurer que le rôle est correctement défini
    if (!req.body.roles) {
      req.body.roles = 'ROLE_MARYEUR';
    }

    // Si un ID personnalisé est fourni, le stocker dans un champ 'id'
    if (req.body.id) {
      // Créer une copie du corps de la requête pour éviter de modifier l'original
      const bodyWithCustomId = { ...req.body };
      // Supprimer l'ID du corps principal pour éviter les conflits avec MongoDB
      delete bodyWithCustomId._id;

      // Créer le mareyeur avec l'ID personnalisé stocké dans un champ 'id'
      const maryeur = new Maryeur(bodyWithCustomId);
      const newMaryeur = await maryeur.save();
      // Standardiser la réponse pour être cohérent avec les autres routes
      res.created({ data: newMaryeur }, 'Mareyeur créé avec succès');
    } else {
      // Création normale sans ID personnalisé
      const maryeur = new Maryeur(req.body);
      const newMaryeur = await maryeur.save();
      // Standardiser la réponse pour être cohérent avec les autres routes
      res.created({ data: newMaryeur }, 'Mareyeur créé avec succès');
    }
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/maryeurs/:id
 * @desc Récupérer un mareyeur spécifique
 * @access Public
 */
router.get('/:id', async (req, res, next) => {
  try {
    let maryeur;

    // Essayer de trouver par ObjectId (MongoDB ID)
    try {
      if (mongoose.Types.ObjectId.isValid(req.params.id)) {
        maryeur = await Maryeur.findById(req.params.id);
      }
    } catch (idError) {
      console.log('Erreur lors de la recherche par ObjectId:', idError);
    }

    // Si non trouvé, essayer de trouver par ID personnalisé
    if (!maryeur) {
      maryeur = await Maryeur.findOne({ id: req.params.id });
    }

    if (!maryeur) {
      throw new NotFoundError('Mareyeur non trouvé');
    }

    // Créer un objet de réponse avec des valeurs par défaut pour les champs null
    const maryeurResponse = {
      id: maryeur.id || maryeur._id.toString(),
      email: maryeur.email || '',
      roles: maryeur.roles || 'ROLE_MARYEUR',
      nom: maryeur.nom || '',
      prenom: maryeur.prenom || '',
      telephone: maryeur.telephone || '',
      photo: maryeur.photo || '',
      societe: maryeur.societe || '',
      registre: maryeur.registre || '',
      adresse: maryeur.adresse || '',
      cin: maryeur.cin || '',
      matricule: maryeur.matricule || '',
      port: maryeur.port || '',
      pays: maryeur.pays || '',
      isValidated: maryeur.isValidated || maryeur.isValid || false,
      isBlocked: maryeur.isBlocked || false
    };

    // Journaliser les informations renvoyées
    console.log(`[${new Date().toISOString()}] INFO [MARYEUR] Détails mareyeur récupérés: ${maryeurResponse.prenom} ${maryeurResponse.nom} (ID: ${maryeurResponse.id})`);

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: maryeurResponse }, 'Mareyeur récupéré avec succès');
  } catch (error) {
    console.log(`[${new Date().toISOString()}] ERROR [MARYEUR] Erreur lors de la récupération du mareyeur: ${error.message}`);
    next(error);
  }
});

/**
 * @route PATCH /api/maryeurs/:id
 * @desc Mettre à jour un mareyeur
 * @access Private (Admin ou le mareyeur lui-même)
 */
router.patch('/:id', auth, async (req, res, next) => {
  try {
    // Vérifier si l'utilisateur est autorisé à modifier ce mareyeur
    if (!req.user.isAdmin() && req.user._id.toString() !== req.params.id) {
      throw new ForbiddenError('Non autorisé à modifier ce mareyeur');
    }

    // Empêcher la modification du rôle par un non-admin
    if (!req.user.isAdmin() && req.body.roles) {
      delete req.body.roles;
    }

    let maryeur;

    // Essayer de mettre à jour par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      maryeur = await Maryeur.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
      });
    }

    // Si non trouvé, essayer de mettre à jour par ID personnalisé
    if (!maryeur) {
      maryeur = await Maryeur.findOneAndUpdate({ id: req.params.id }, req.body, {
        new: true,
        runValidators: true
      });
    }

    if (!maryeur) {
      throw new NotFoundError('Mareyeur non trouvé');
    }

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: maryeur }, 'Mareyeur mis à jour avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/maryeurs/:id
 * @desc Supprimer un mareyeur
 * @access Private (Admin uniquement)
 */
router.delete('/:id', auth, checkRole('ROLE_ADMIN'), async (req, res, next) => {
  try {
    let maryeur;
    let deleteResult;

    // Essayer de supprimer par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      deleteResult = await Maryeur.findByIdAndDelete(req.params.id);
      if (deleteResult) {
        maryeur = deleteResult;
      }
    }

    // Si non trouvé, essayer de supprimer par ID personnalisé
    if (!maryeur) {
      deleteResult = await Maryeur.findOneAndDelete({ id: req.params.id });
      if (deleteResult) {
        maryeur = deleteResult;
      }
    }

    if (!maryeur) {
      throw new NotFoundError('Mareyeur non trouvé');
    }

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: null }, 'Mareyeur supprimé avec succès');
  } catch (error) {
    next(error);
  }
});

module.exports = router;