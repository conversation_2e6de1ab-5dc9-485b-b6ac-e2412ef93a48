// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'سي تريس';

  @override
  String get loginTitle => 'تسجيل الدخول';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'التسجيل';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get serverIp => 'عنوان IP للخادم';

  @override
  String get testConnection => 'اختبار الاتصال';

  @override
  String get applyIp => 'تطبيق';

  @override
  String get discoverServers => 'اكتشاف الخوادم';

  @override
  String welcomeBack(Object name) {
    return 'مرحبًا، $name';
  }

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get noNotifications => 'لا توجد إشعارات';

  @override
  String get markAllAsRead => 'تحديد الكل كمقروء';

  @override
  String get language => 'اللغة';

  @override
  String get french => 'الفرنسية';

  @override
  String get arabic => 'العربية';

  @override
  String get chooseLanguage => 'اختر اللغة';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجاح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get noData => 'لا توجد بيانات متاحة';

  @override
  String get scanFish => 'مسح السمكة';

  @override
  String get fishDetails => 'تفاصيل السمكة';

  @override
  String get weight => 'الوزن';

  @override
  String get temperature => 'درجة الحرارة';

  @override
  String get fishingMethod => 'طريقة الصيد';

  @override
  String get fishingZone => 'منطقة الصيد';

  @override
  String get location => 'الموقع';

  @override
  String get selectMareyeur => 'اختر تاجر السمك';

  @override
  String get selectVeterinarian => 'اختر الطبيب البيطري';

  @override
  String get submit => 'إرسال';

  @override
  String get pendingLots => 'الدفعات المعلقة';

  @override
  String get approvedLots => 'الدفعات المعتمدة';

  @override
  String get rejectedLots => 'الدفعات المرفوضة';

  @override
  String get viewPendingLots => 'عرض الدفعات المعلقة';

  @override
  String get statistics => 'الإحصائيات';

  @override
  String get recentActivity => 'النشاط الأخير';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get noRecentActivity => 'لا يوجد نشاط حديث';

  @override
  String get approve => 'موافقة';

  @override
  String get reject => 'رفض';

  @override
  String get rejectionReason => 'سبب الرفض';

  @override
  String get initialPrice => 'السعر الأولي';

  @override
  String get minimalPrice => 'السعر الأدنى';

  @override
  String get startAuction => 'بدء المزاد';

  @override
  String get activeAuctions => 'المزادات النشطة';

  @override
  String get completedAuctions => 'المزادات المكتملة';

  @override
  String get availableAuctions => 'المزادات المتاحة';

  @override
  String get myPurchases => 'مشترياتي';

  @override
  String get bid => 'مزايدة';

  @override
  String get currentBid => 'المزايدة الحالية';

  @override
  String get timeRemaining => 'الوقت المتبقي';

  @override
  String get extendAuction => 'تمديد المزاد';

  @override
  String get closeAuction => 'إغلاق المزاد';

  @override
  String get auctionClosed => 'تم إغلاق المزاد';

  @override
  String get youWon => 'لقد فزت بهذا المزاد!';

  @override
  String get confirmSpecies => 'تأكيد هذا النوع';

  @override
  String get refreshCounters => 'تحديث العدادات';

  @override
  String get countersRefreshed => 'تم تحديث العدادات بنجاح';

  @override
  String get changeLanguage => 'تغيير اللغة';

  @override
  String get languageChanged => 'تم تغيير اللغة بنجاح';
}
