const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = require('../middleware/upload');
const { uploadImage, getImage, deleteImage } = require('../controllers/imageController');
const { auth } = require('../middleware/auth');

// Middleware pour gérer les erreurs de multer
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // Erreur Multer spécifique
    console.error('Erreur Multer:', err);
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'La taille du fichier dépasse la limite de 5 MB'
      });
    }
    return res.status(400).json({ error: `Erreur d'upload: ${err.message}` });
  } else if (err) {
    // Autre erreur
    console.error('Erreur lors de l\'upload:', err);
    return res.status(500).json({ error: err.message });
  }
  next();
};

// Route pour télécharger une image (nécessite authentification)
router.post(
  '/upload',
  auth,
  upload.single('image'),
  (err, req, res, next) => {
    // Gestion d'erreur Multer en middleware dédié (optionnel)
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ error: 'La taille du fichier dépasse la limite de 5 MB' });
      }
      return res.status(400).json({ error: `Erreur d'upload: ${err.message}` });
    }
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    next();
  },
  (req, res, next) => {
    // Ici Multer a déjà traité la requête, donc req.file est défini si upload réussi
    console.log('Fichier reçu :', req.file);
    console.log('Autres champs:', req.body);
    next();
  },
  uploadImage // contrôleur final
);


// Route pour récupérer une image par son nom de fichier (publique)
router.get('/:filename', getImage);

// Route pour supprimer une image (nécessite authentification)
router.delete('/:filename', auth, deleteImage);

module.exports = router;
