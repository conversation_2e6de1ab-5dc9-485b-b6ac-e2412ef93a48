import 'package:seatrace/models/base_user.dart';

/// <PERSON><PERSON><PERSON><PERSON> représentant un client dans l'application
/// Correspond au modèle Client côté backend
class Client extends BaseUser {
  final String? adresse;
  final List<String>? achats;

  Client({
    super.id,
    required super.email,
    required super.roles,
    required super.password,
    required super.nom,
    required super.prenom,
    super.telephone,
    super.photo,
    super.isValidated = false,
    super.isBlocked = false,
    super.createdAt,
    super.updatedAt,
    this.adresse,
    this.achats,
  });

  // Convertir un objet Map en objet Client
  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['_id']?.toString() ?? map['id']?.toString() ?? '',
      email: map['email'] ?? '',
      roles: map['roles'] ?? '',
      password: map['password'] ?? '',
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      telephone: map['telephone']?.toString(),
      adresse: map['adresse'],
      photo: map['photo'],
      isValidated: map['isValidated'] ?? map['isValid'] ?? false,
      isBlocked: map['isBlocked'] ?? false,
      createdAt: BaseUser.parseDateTime(map['createdAt']),
      updatedAt: BaseUser.parseDateTime(map['updatedAt']),
      achats:
          map['achats'] != null
              ? List<String>.from(map['achats'].map((x) => x.toString()))
              : null,
    );
  }

  // Convertir un objet Client en objet Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'roles': roles,
      'password': password,
      'nom': nom,
      'prenom': prenom,
      'telephone': telephone,
      'adresse': adresse,
      'photo': photo,
      'isValidated': isValidated,
      'isBlocked': isBlocked,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'achats': achats,
    };
  }

  // Créer une copie de l'objet avec des modifications
  Client copyWith({
    String? id,
    String? email,
    String? roles,
    String? password,
    String? nom,
    String? prenom,
    String? telephone,
    String? adresse,

    String? photo,
    bool? isValidated,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? achats,
  }) {
    return Client(
      id: id ?? this.id,
      email: email ?? this.email,
      roles: roles ?? this.roles,
      password: password ?? this.password,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      telephone: telephone ?? this.telephone,
      adresse: adresse ?? this.adresse,
      photo: photo ?? this.photo,
      isValidated: isValidated ?? this.isValidated,
      isBlocked: isBlocked ?? this.isBlocked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      achats: achats ?? this.achats,
    );
  }
}
