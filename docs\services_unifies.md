# Documentation des Services Unifiés

## Introduction

Les services unifiés ont été créés pour standardiser et centraliser les appels API et les fonctionnalités communes dans l'application SeaTrace. Cette approche permet de réduire la duplication de code, d'améliorer la cohérence et de faciliter la maintenance.

La migration vers les services unifiés est maintenant **terminée**. Tous les anciens services ont été remplacés par les nouveaux services unifiés, et tous les écrans ont été migrés pour utiliser ces nouveaux services.

## Services disponibles

### UnifiedApiService

Service de base pour les appels API.

```dart
import 'package:seatrace/services/unified_api_service.dart';

final apiService = UnifiedApiService();
```

#### Méthodes principales

- `get(String endpoint)` : Effectue une requête GET
- `post(String endpoint, Map<String, dynamic> data)` : Effectue une requête POST
- `put(String endpoint, Map<String, dynamic> data)` : Effectue une requête PUT
- `delete(String endpoint)` : Effectue une requête DELETE
- `uploadFile(String endpoint, String filePath)` : Télécharge un fichier
- `getBaseUrl()` : Retourne l'URL de base de l'API

#### Méthodes spécifiques

- `getPecheur(String id)` : Récupère les détails d'un pêcheur
- `getVeterinaire(String id)` : Récupère les détails d'un vétérinaire
- `getMaryeur(String id)` : Récupère les détails d'un maryeur
- `getClient(String id)` : Récupère les détails d'un client
- `getLot(String id)` : Récupère les détails d'un lot

### UnifiedAuthService

Service pour la gestion de l'authentification.

```dart
import 'package:seatrace/services/unified_auth_service.dart';

final authService = UnifiedAuthService();
```

#### Méthodes principales

- `login(String email, String password)` : Connecte un utilisateur
- `register(Map<String, dynamic> userData)` : Inscrit un nouvel utilisateur
- `logout()` : Déconnecte l'utilisateur
- `getCurrentUser()` : Récupère l'utilisateur connecté
- `isLoggedIn()` : Vérifie si un utilisateur est connecté

### UnifiedLotService

Service pour la gestion des lots et des enchères.

```dart
import 'package:seatrace/services/unified_lot_service.dart';

final lotService = UnifiedLotService();
```

#### Méthodes principales

- `getPendingLots()` : Récupère les lots en attente pour un vétérinaire
- `approveLot(String lotId)` : Approuve un lot
- `rejectLot(String lotId)` : Rejette un lot
- `getActiveAuctions()` : Récupère les enchères actives pour un maryeur
- `getAvailableAuctions()` : Récupère les enchères disponibles pour un client
- `placeBid(String auctionId, double bidAmount)` : Place une enchère
- `getAuctionDetails(String auctionId)` : Récupère les détails d'une enchère
- `getLotById(String lotId)` : Récupère un lot par son ID
- `getLotsByPecheur()` : Récupère les lots d'un pêcheur
- `createLot(Map<String, dynamic> lotData)` : Crée un nouveau lot
- `setMinPrice(String lotId, double minPrice)` : Définit le prix minimal pour un lot
- `startAuction(String lotId, DateTime endDate)` : Démarre une enchère
- `endAuction(String lotId)` : Termine une enchère

### UnifiedNotificationService

Service pour la gestion des notifications.

```dart
import 'package:seatrace/services/unified_notification_service.dart';

final notificationService = UnifiedNotificationService();
```

#### Méthodes principales

- `initialize()` : Initialise le service de notifications
- `getNotifications()` : Récupère les notifications de l'utilisateur
- `markAsRead(String notificationId)` : Marque une notification comme lue
- `markAllAsRead()` : Marque toutes les notifications comme lues
- `deleteNotification(String notificationId)` : Supprime une notification
- `getUnreadCount()` : Récupère le nombre de notifications non lues

### UnifiedProfileService

Service pour la gestion des profils utilisateurs.

```dart
import 'package:seatrace/services/unified_profile_service.dart';

final profileService = UnifiedProfileService();
```

#### Méthodes principales

- `getCurrentUser()` : Récupère l'utilisateur connecté
- `updateProfile(dynamic user)` : Met à jour le profil utilisateur
- `changePassword(String oldPassword, String newPassword)` : Change le mot de passe
- `uploadProfilePhoto(String filePath)` : Télécharge une photo de profil
- `getUserType(dynamic user)` : Obtient le type d'utilisateur

## Utilisation

### Exemple d'utilisation dans un écran

```dart
import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_lot_service.dart';

class MyScreen extends StatefulWidget {
  @override
  _MyScreenState createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen> {
  final _authService = UnifiedAuthService();
  final _lotService = UnifiedLotService();

  Future<void> _loadData() async {
    final user = await _authService.getCurrentUser();
    if (user != null) {
      final lots = await _lotService.getLotsByPecheur();
      // Utiliser les lots...
    }
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  // ...
}
```

## Standardisation des modèles

Les modèles ont été standardisés pour assurer une meilleure cohérence entre le frontend et le backend :

1. Création d'une classe de base **BaseUser** dont héritent tous les modèles d'utilisateurs
2. Standardisation des champs communs à tous les utilisateurs
3. Implémentation de méthodes communes pour la gestion des rôles et des IDs
4. Ajout de méthodes de conversion entre les formats de données

### Exemple d'utilisation de BaseUser

```dart
import 'package:seatrace/models/base_user.dart';

// Vérifier le type d'utilisateur
if (BaseUser.isPecheur(roles)) {
  // Logique spécifique aux pêcheurs
}

// Utiliser les méthodes d'instance
if (user.isPecheurRole()) {
  // Logique spécifique aux pêcheurs
}
```

## Avantages des services unifiés

1. **Réduction de la duplication de code** : Les fonctionnalités communes sont centralisées
2. **Cohérence** : Les appels API et les modèles de données sont standardisés
3. **Facilité de maintenance** : Les modifications sont plus faciles à implémenter
4. **Meilleure gestion des erreurs** : Les erreurs sont gérées de manière centralisée
5. **Facilité d'utilisation** : Les services sont faciles à utiliser et à comprendre
6. **Robustesse** : Les services unifiés incluent une meilleure gestion des erreurs et des cas limites
7. **Standardisation des modèles** : Les modèles sont cohérents et héritent d'une classe de base commune

## Prochaines étapes

1. **Tests** : Exécuter des tests approfondis pour s'assurer que tout fonctionne correctement
2. **Documentation** : Continuer à mettre à jour la documentation pour refléter la nouvelle architecture
3. **Optimisation** : Optimiser les performances des services unifiés
4. **Nouvelles fonctionnalités** : Ajouter de nouvelles fonctionnalités en utilisant les services unifiés
