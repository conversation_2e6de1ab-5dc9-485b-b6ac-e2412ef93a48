{"name": "peche-app-backend", "version": "1.0.0", "description": "Backend pour l'application de marché aux poissons", "main": "src/index.js", "scripts": {"start": "node ./src/index.js", "dev": "nodemon ./src/index.js", "migrate-users": "node ./src/scripts/migrateUsers.js", "create-admin": "node ./src/scripts/create-admin.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "sharp": "^0.33.2", "uuid": "^11.1.0", "validator": "^13.11.0", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}}