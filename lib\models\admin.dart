import 'package:seatrace/models/base_user.dart';

/// Modèle représentant un administrateur dans l'application
/// Correspond au modèle Admin côté backend
class Admin extends BaseUser {
  final String? permissions;
  final DateTime? lastLogin;

  Admin({
    super.id,
    required super.email,
    required super.roles,
    required super.password,
    required super.nom,
    required super.prenom,
    super.telephone,
    super.photo,
    super.isValidated = true,
    super.isBlocked = false,
    super.createdAt,
    super.updatedAt,
    this.permissions,
    this.lastLogin,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'roles': roles,
      'password': password,
      'nom': nom,
      'prenom': prenom,
      'telephone': telephone,
      'photo': photo,
      'isValidated': isValidated,
      'isBlocked': isBlocked,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'permissions': permissions,
      'lastLogin': lastLogin,
    };
  }

  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      id: json['id'] ?? json['_id'],
      email: json['email'] ?? '',
      roles: json['roles'] ?? 'ROLE_ADMIN',
      password: json['password'] ?? '',
      nom: json['nom'] ?? '',
      prenom: json['prenom'] ?? '',
      telephone: json['telephone'],
      photo: json['photo'],
      isValidated: json['isValidated'] ?? true,
      isBlocked: json['isBlocked'] ?? false,
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
      permissions: json['permissions'],
      lastLogin: BaseUser.parseDateTime(json['lastLogin']),
    );
  }

  // Retourne le type d'utilisateur
  String getUserType() {
    return 'admin';
  }
}
