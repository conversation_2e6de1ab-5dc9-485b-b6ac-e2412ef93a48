import 'dart:async';
import 'package:flutter/material.dart';
import 'package:seatrace/models/notification.dart' as model;
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/websocket_service.dart';
import 'package:seatrace/utils/error_handler.dart';

/// Service unifié pour la gestion des notifications
class UnifiedNotificationService {
  static final UnifiedNotificationService _instance =
      UnifiedNotificationService._internal();
  factory UnifiedNotificationService() => _instance;

  /// Liste des notifications
  List<model.Notification> _notifications = [];

  /// Nombre de notifications non lues
  int _unreadCount = 0;

  /// Contrôleur de flux pour les notifications
  final _notificationsController =
      StreamController<List<model.Notification>>.broadcast();

  /// Contrôleur de flux pour le nombre de notifications non lues
  final _unreadCountController = StreamController<int>.broadcast();

  /// Intervalle de rafraîchissement des notifications (en secondes)
  final int _refreshInterval = 30;

  /// Timer pour le rafraîchissement automatique des notifications
  Timer? _refreshTimer;

  /// Indique si le service est initialisé
  bool _isInitialized = false;

  /// Service API unifié
  final UnifiedApiService _apiService = UnifiedApiService();

  /// Service WebSocket
  final WebSocketService _webSocketService = WebSocketService();

  UnifiedNotificationService._internal();

  /// Flux de notifications
  Stream<List<model.Notification>> get notifications =>
      _notificationsController.stream;

  /// Flux du nombre de notifications non lues
  Stream<int> get unreadCount => _unreadCountController.stream;

  /// Initialise le service de notifications
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialiser le service WebSocket
      await _webSocketService.initialize();

      // Écouter les notifications WebSocket
      _webSocketService.notifications.listen(_handleWebSocketNotification);

      // Charger les notifications
      await fetchNotifications();

      // Démarrer le rafraîchissement automatique
      _startAutoRefresh();

      _isInitialized = true;

      // Journaliser l'initialisation
      ErrorHandler.instance.logInfo(
        'Service de notifications unifié initialisé',
        context: 'UnifiedNotificationService',
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.initialize',
      );
    }
  }

  /// Gère les notifications reçues via WebSocket
  void _handleWebSocketNotification(model.Notification notification) {
    // Ajouter la notification à la liste
    _notifications.insert(0, notification);

    // Incrémenter le compteur si la notification n'est pas lue
    if (!notification.lue) {
      _unreadCount++;
    }

    // Émettre les nouvelles valeurs
    _notificationsController.add(_notifications);
    _unreadCountController.add(_unreadCount);

    // Journaliser la réception
    ErrorHandler.instance.logInfo(
      'Notification reçue via WebSocket: ${notification.titre}',
      context: 'UnifiedNotificationService',
    );
  }

  /// Récupère les notifications depuis l'API
  Future<List<model.Notification>> fetchNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      // Vérifier si l'utilisateur est connecté
      final user = await _apiService.getCurrentUser();
      if (user == null) {
        ErrorHandler.instance.logWarning(
          'Tentative de récupération des notifications sans utilisateur connecté',
          context: 'UnifiedNotificationService.fetchNotifications',
        );
        return [];
      }

      // Journaliser la requête
      ErrorHandler.instance.logInfo(
        'Récupération des notifications (page: $page, limit: $limit)',
        context: 'UnifiedNotificationService.fetchNotifications',
      );

      try {
        // Récupérer les notifications
        final response = await _apiService.get(
          'notifications',
          queryParameters: {'page': page.toString(), 'limit': limit.toString()},
        );

        // Journaliser la réponse pour le débogage
        ErrorHandler.instance.logInfo(
          'Réponse de l\'API pour les notifications: ${response.toString().substring(0, response.toString().length > 100 ? 100 : response.toString().length)}...',
          context: 'UnifiedNotificationService.fetchNotifications',
        );

        // Standardisation de la structure de réponse
        List<dynamic> data = [];
        int nonLues = 0;

        // Nouvelle structure standardisée
        if (response.containsKey('data') && response['data'] is List) {
          data = response['data'] as List<dynamic>;
          debugPrint(
            'Données extraites du champ data: ${data.length} éléments',
          );

          // Extraire le nombre de notifications non lues
          if (response.containsKey('stats') &&
              response['stats'] is Map &&
              response['stats'].containsKey('nonLues')) {
            nonLues = response['stats']['nonLues'] ?? 0;
          }
        }
        // Ancienne structure pour compatibilité
        else if (response.containsKey('notifications') &&
            response['notifications'] is List) {
          data = response['notifications'] as List<dynamic>;
          debugPrint(
            'Données extraites du champ notifications (ancienne structure): ${data.length} éléments',
          );

          // Extraire le nombre de notifications non lues
          if (response.containsKey('stats') &&
              response['stats'] is Map &&
              response['stats'].containsKey('nonLues')) {
            nonLues = response['stats']['nonLues'] ?? 0;
          }
        } else {
          debugPrint(
            'Structure de réponse inattendue, utilisation d\'une liste vide',
          );
          ErrorHandler.instance.logWarning(
            'Structure de réponse inattendue pour les notifications: ${response.toString()}',
            context: 'UnifiedNotificationService.fetchNotifications',
          );
        }

        // Convertir les données en objets Notification
        final notifications =
            data.map((item) {
              try {
                return model.Notification.fromMap(item);
              } catch (mapError) {
                ErrorHandler.instance.logError(
                  'Erreur lors de la conversion d\'une notification: $mapError\nDonnées: $item',
                  context: 'UnifiedNotificationService.fetchNotifications',
                );
                // Retourner une notification vide en cas d'erreur
                return model.Notification(
                  id: item['_id'] ?? item['id'] ?? '',
                  destinataire: '',
                  destinataireModel: '',
                  titre: item['titre'] ?? 'Notification',
                  contenu: item['contenu'] ?? 'Contenu non disponible',
                  type: 'info',
                  lue: item['lue'] ?? false,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );
              }
            }).toList();

        // Mettre à jour les données
        _notifications = notifications;
        _unreadCount = nonLues;

        // Émettre les nouvelles valeurs
        _notificationsController.add(_notifications);
        _unreadCountController.add(_unreadCount);

        return notifications;
      } catch (apiError) {
        ErrorHandler.instance.logError(
          'Erreur lors de la récupération des notifications: $apiError',
          context: 'UnifiedNotificationService.fetchNotifications.apiCall',
        );

        // En cas d'erreur, conserver les notifications existantes
        // mais ne pas bloquer l'application
        return _notifications;
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.fetchNotifications',
      );
      return [];
    }
  }

  /// Marque une notification comme lue
  Future<bool> markAsRead(String notificationId) async {
    try {
      // Appeler l'API
      await _apiService.patch('notifications/$notificationId/lue', {});

      // Mettre à jour la notification localement
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        final notification = _notifications[index];
        if (!notification.lue) {
          // Créer une nouvelle notification avec lue = true
          final updatedNotification = notification.copyWith(lue: true);

          // Mettre à jour la liste
          _notifications[index] = updatedNotification;

          // Mettre à jour le compteur
          _unreadCount = _unreadCount > 0 ? _unreadCount - 1 : 0;

          // Émettre les nouvelles valeurs
          _notificationsController.add(_notifications);
          _unreadCountController.add(_unreadCount);
        }
      }

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.markAsRead',
      );
      return false;
    }
  }

  /// Marque toutes les notifications comme lues
  Future<bool> markAllAsRead() async {
    try {
      // Appeler l'API
      final response = await _apiService.patch('notifications/lire-tout', {});

      // Mettre à jour les notifications localement
      final count = response['count'] ?? 0;
      if (count > 0) {
        // Mettre à jour toutes les notifications
        _notifications =
            _notifications.map((n) => n.copyWith(lue: true)).toList();

        // Mettre à jour le compteur
        _unreadCount = 0;

        // Émettre les nouvelles valeurs
        _notificationsController.add(_notifications);
        _unreadCountController.add(_unreadCount);
      }

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.markAllAsRead',
      );
      return false;
    }
  }

  /// Supprime une notification
  Future<bool> deleteNotification(String notificationId) async {
    try {
      // Appeler l'API
      await _apiService.delete('notifications/$notificationId');

      // Supprimer la notification localement
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        final notification = _notifications[index];

        // Supprimer de la liste
        _notifications.removeAt(index);

        // Mettre à jour le compteur si la notification n'était pas lue
        if (!notification.lue) {
          _unreadCount = _unreadCount > 0 ? _unreadCount - 1 : 0;
          _unreadCountController.add(_unreadCount);
        }

        // Émettre les nouvelles valeurs
        _notificationsController.add(_notifications);
      }

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.deleteNotification',
      );
      return false;
    }
  }

  /// Récupère une notification par son ID
  Future<model.Notification?> getNotificationById(String notificationId) async {
    try {
      // Vérifier d'abord dans la liste locale
      final notification = _notifications.firstWhere(
        (n) => n.id == notificationId,
        orElse:
            () => model.Notification(
              id: '',
              destinataire: '',
              destinataireModel: '',
              titre: '',
              contenu: '',
              type: 'info',
              lue: false,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
      );

      if (notification.id.isNotEmpty) {
        return notification;
      }

      // Si non trouvée localement, essayer de la récupérer depuis l'API
      final response = await _apiService.get('notifications/$notificationId');
      return model.Notification.fromMap(response);
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.getNotificationById',
      );
      return null;
    }
  }

  /// Démarre le rafraîchissement automatique des notifications
  void _startAutoRefresh() {
    // Annuler le timer existant s'il y en a un
    _refreshTimer?.cancel();

    // Créer un nouveau timer
    _refreshTimer = Timer.periodic(
      Duration(seconds: _refreshInterval),
      (_) => fetchNotifications(),
    );
  }

  /// Arrête le rafraîchissement automatique des notifications
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Libère les ressources
  void dispose() {
    stopAutoRefresh();
    _webSocketService.dispose();
    _notificationsController.close();
    _unreadCountController.close();
    _isInitialized = false;
  }

  /// Crée une nouvelle notification
  Future<bool> createNotification({
    required String destinataireId,
    required String destinataireType,
    required String titre,
    required String contenu,
    required String type,
    String? referenceId,
    String? referenceType,
    String? urlAction,
  }) async {
    try {
      // Journaliser les données de la notification pour le débogage
      ErrorHandler.instance.logInfo(
        'Création d\'une notification: destinataire=$destinataireId, type=$destinataireType, titre=$titre',
        context: 'UnifiedNotificationService.createNotification',
      );

      final data = {
        'destinataire': destinataireId,
        'destinataireModel': destinataireType,
        'titre': titre,
        'contenu': contenu,
        'type': type,
        if (referenceId != null) 'reference': referenceId,
        if (referenceType != null) 'referenceModel': referenceType,
        if (urlAction != null) 'urlAction': urlAction,
      };

      // Journaliser les données complètes
      ErrorHandler.instance.logInfo(
        'Données de notification: $data',
        context: 'UnifiedNotificationService.createNotification',
      );

      final response = await _apiService.post('notifications', data);

      // Journaliser la réponse
      ErrorHandler.instance.logInfo(
        'Réponse de création de notification: $response',
        context: 'UnifiedNotificationService.createNotification',
      );

      // Rafraîchir les notifications si l'utilisateur actuel est le destinataire
      final currentUser = await _apiService.getCurrentUser();
      if (currentUser != null && currentUser.id == destinataireId) {
        await fetchNotifications();
      }

      final success =
          response.containsKey('success') ? response['success'] : true;

      if (success) {
        ErrorHandler.instance.logInfo(
          'Notification créée avec succès pour $destinataireType: $destinataireId',
          context: 'UnifiedNotificationService.createNotification',
        );
      } else {
        ErrorHandler.instance.logWarning(
          'Échec de création de la notification: ${response['message'] ?? 'Raison inconnue'}',
          context: 'UnifiedNotificationService.createNotification',
        );
      }

      return success;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedNotificationService.createNotification',
      );
      return false;
    }
  }
}
