import 'package:seatrace/models/base_user.dart';

/// Modèle représentant un pêcheur dans l'application
/// Correspond au modèle Pecheur côté backend
class Pecheur extends BaseUser {
  final String? cin;
  final String? matricule;

  // Informations sur le bateau et l'activité de pêche (champs essentiels uniquement)
  final String? capacite;
  final String? longueur; // Correction de l'orthographe
  String? get longeur => longueur; // Getter pour la compatibilité
  final String? largeur;
  final String? bateau;
  final String? port;
  final String? engin;

  // Liste des prises associées à ce pêcheur
  final List<String>? prises;

  Pecheur({
    super.id,
    required super.email,
    required super.roles,
    required super.password,
    required super.nom,
    required super.prenom,
    super.telephone,
    super.photo,
    super.isValidated = false,
    super.isBlocked = false,
    super.createdAt,
    super.updatedAt,
    this.cin,
    this.matricule,
    this.capacite,
    this.longueur,
    this.largeur,
    this.bateau,
    this.port,
    this.engin,
    this.prises,
  });

  factory Pecheur.fromMap(Map<String, dynamic> map) {
    return Pecheur(
      id: map['_id']?.toString() ?? map['id']?.toString(),
      email: map['email'] ?? '',
      roles: map['roles'] ?? '',
      password: map['password'] ?? '',
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      cin: map['cin'],
      matricule: map['matricule'],
      capacite: map['capacite'],
      longueur:
          map['longueur'] ?? map['longeur'], // Accepter les deux orthographes
      largeur: map['largeur'],
      bateau: map['bateau'],
      port: map['port'],
      engin: map['engin'],
      telephone: map['telephone']?.toString(),
      photo: map['photo'],
      isValidated: map['isValidated'] ?? map['isValid'] ?? false,
      isBlocked: map['isBlocked'] ?? false,
      createdAt: BaseUser.parseDateTime(map['createdAt']),
      updatedAt: BaseUser.parseDateTime(map['updatedAt']),
      prises:
          map['prises'] != null
              ? List<String>.from(map['prises'].map((x) => x.toString()))
              : null,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'roles': roles,
      'password': password,
      'nom': nom,
      'prenom': prenom,
      'cin': cin,
      'matricule': matricule,
      'capacite': capacite,
      'longueur': longueur,
      'largeur': largeur,
      'bateau': bateau,
      'port': port,
      'engin': engin,
      'telephone': telephone,
      'photo': photo,
      'isValidated': isValidated,
      'isBlocked': isBlocked,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'prises': prises,
    }..removeWhere((key, value) => value == null);
  }

  /// Créer une copie de l'objet avec des modifications
  Pecheur copyWith({
    String? id,
    String? email,
    String? roles,
    String? password,
    String? nom,
    String? prenom,
    String? cin,
    String? matricule,
    String? capacite,
    String? longueur,
    String? largeur,
    String? bateau,
    String? port,
    String? engin,
    String? telephone,
    String? photo,
    bool? isValidated,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? prises,
  }) {
    return Pecheur(
      id: id ?? this.id,
      email: email ?? this.email,
      roles: roles ?? this.roles,
      password: password ?? this.password,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      cin: cin ?? this.cin,
      matricule: matricule ?? this.matricule,
      capacite: capacite ?? this.capacite,
      longueur: longueur ?? this.longueur,
      largeur: largeur ?? this.largeur,
      bateau: bateau ?? this.bateau,
      port: port ?? this.port,
      engin: engin ?? this.engin,
      telephone: telephone ?? this.telephone,
      photo: photo ?? this.photo,
      isValidated: isValidated ?? this.isValidated,
      isBlocked: isBlocked ?? this.isBlocked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      prises: prises ?? this.prises,
    );
  }
}
