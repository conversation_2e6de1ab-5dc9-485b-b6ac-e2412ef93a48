import 'package:flutter/foundation.dart';
import 'package:seatrace/dtos/lot_dto.dart';
import 'package:seatrace/models/lot.dart';

/// Service pour convertir les données entre les modèles et les DTOs
/// Version simplifiée sans Espece et Prise
class DtoService {
  static final DtoService instance = DtoService._internal();

  DtoService._internal();

  /// Convertit un Map (JSON) en LotDto
  LotDto jsonToLotDto(Map<String, dynamic> json) {
    try {
      return LotDto.fromJson(json);
    } catch (e) {
      debugPrint('Erreur lors de la conversion JSON -> LotDto: $e');
      rethrow;
    }
  }

  /// Convertit un Lot en LotDto
  ///
  /// Cette méthode standardise la conversion d'un objet Lot en LotDto
  /// Version simplifiée sans référence à Espece ou Prise
  ///
  /// @param lot L'objet Lot à convertir
  /// @return Un objet LotDto standardisé
  LotDto lotToDto(Lot lot) {
    try {
      return LotDto(
        id: lot.id,
        identifiant: lot.identifiant,
        photo: lot.photo,
        quantite: lot.quantite,
        poids: lot.poids,
        especeNom: lot.especeNom,
        temperature: lot.temperature,
        dateTest: lot.dateTest?.toIso8601String() ?? '',
        test: lot.test,
        isValidated: lot.isValidated,
        vendu: lot.vendu,
        userId: lot.userId,
        dateSoumission: lot.dateSoumission?.toIso8601String() ?? '',
        prixInitial: lot.prixInitial,
        prixMinimal: lot.prixMinimal,
        prixFinal: lot.prixFinal,
        acheteurId: lot.acheteurId,
      );
    } catch (e) {
      debugPrint('Erreur lors de la conversion Lot -> LotDto: $e');
      rethrow;
    }
  }

  /// Convertit une liste de Map (JSON) en liste de DTOs
  List<T> jsonListToDtoList<T>(
    List<dynamic> jsonList,
    T Function(Map<String, dynamic>) converter,
  ) {
    try {
      return jsonList
          .map((json) => converter(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Erreur lors de la conversion JSON List -> DTO List: $e');
      rethrow;
    }
  }
}
