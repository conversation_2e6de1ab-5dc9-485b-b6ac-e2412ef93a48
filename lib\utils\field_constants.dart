/// Constantes pour les noms de champs standardisés entre le frontend et le backend
///
/// Ce fichier définit les noms de champs à utiliser de manière cohérente
/// dans toute l'application, tant côté frontend que backend.
/// Il permet d'éviter les incohérences de nommage et facilite la maintenance.
class FieldConstants {
  // Champs communs
  static const String id = 'id';
  static const String mongoId = '_id';
  static const String createdAt = 'createdAt';
  static const String updatedAt = 'updatedAt';

  // Champs de validation
  static const String isValid = 'isValid';
  static const String isValidated = 'isValidated';
  static const String isBlocked = 'isBlocked';
  static const String status = 'status';

  // Champs utilisateur
  static const String email = 'email';
  static const String password = 'password';
  static const String nom = 'nom';
  static const String prenom = 'prenom';
  static const String telephone = 'telephone';
  static const String photo = 'photo';
  static const String roles = 'roles';
  static const String userType = 'userType';

  // Champs de référence
  static const String pecheurId = 'pecheurId';
  static const String pecheur = 'pecheur';
  static const String veterinaireId = 'veterinaireId';
  static const String veterinaire = 'veterinaire';
  static const String maryeurId = 'maryeurId';
  static const String maryeur = 'maryeur';
  static const String clientId = 'clientId';
  static const String client = 'client';
  static const String acheteurId = 'acheteurId';
  static const String acheteur = 'acheteur';
  static const String userId = 'userId';
  static const String user = 'user';

  // Champs de lot
  static const String identifiant = 'identifiant';
  static const String quantite = 'quantite';
  static const String poids = 'poids';
  static const String temperature = 'temperature';
  static const String dateTest = 'dateTest';
  static const String test = 'test';
  static const String vendu = 'vendu';
  static const String priseId = 'priseId';
  static const String prise = 'prise';
  static const String dateSoumission = 'dateSoumission';
  static const String isProduit = 'isProduit';
  static const String prixInitial = 'prixInitial';
  static const String prixMinimal = 'prixMinimal';
  static const String prixFinal = 'prixFinal';
  static const String isAuctionActive = 'isAuctionActive';
  static const String dateFinEnchere = 'dateFinEnchere';
  static const String motifRefus = 'motifRefus';

  // Champs d'espèce
  static const String especeId = 'especeId';
  static const String espece = 'espece';
  static const String especeNom = 'especeNom';
  static const String description = 'description';
  static const String nomScientifique = 'nomScientifique';
  static const String prixMoyen = 'prixMoyen';
  static const String isActive = 'isActive';
  static const String confiance = 'confiance';
  static const String source = 'source';
  static const String alternatives = 'alternatives';
  static const String saison = 'saison';
  static const String habitat = 'habitat';
  static const String methodePeche = 'methodePeche';

  // Champs de prise
  static const String date = 'date';
  static const String lieu = 'lieu';
  static const String latitude = 'latitude';
  static const String longitude = 'longitude';
  static const String debut = 'debut';
  static const String fin = 'fin';
  static const String engin = 'engin';
  static const String zone = 'zone';
  static const String affectationDate = 'affectationDate';
  static const String dateDebarquement = 'dateDebarquement';

  // Champs d'image
  static const String imageUrl = 'imageUrl';
}
