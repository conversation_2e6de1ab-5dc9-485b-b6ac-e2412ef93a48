import 'package:flutter/material.dart';
import 'package:seatrace/services/language_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/widgets/language_selector.dart';
import 'package:seatrace/screens/login_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _languageService = LanguageService();
  
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: ValueListenableBuilder<Locale>(
          valueListenable: _languageService.languageNotifier,
          builder: (context, locale, _) {
            return Text(_getLocalizedText('settings', locale.languageCode));
          },
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Messages d'erreur ou de succès
                    if (_errorMessage != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: theme.colorScheme.error),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: theme.colorScheme.error),
                            const SizedBox(width: 8),
                            Expanded(child: Text(_errorMessage!)),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => setState(() => _errorMessage = null),
                            ),
                          ],
                        ),
                      ),
                    
                    if (_successMessage != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.green),
                            const SizedBox(width: 8),
                            Expanded(child: Text(_successMessage!)),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => setState(() => _successMessage = null),
                            ),
                          ],
                        ),
                      ),
                    
                    // Section langue
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.language,
                                    size: 24,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ValueListenableBuilder<Locale>(
                                    valueListenable: _languageService.languageNotifier,
                                    builder: (context, locale, _) {
                                      return Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            _getLocalizedText('language', locale.languageCode),
                                            style: theme.textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            _getLocalizedText('chooseLanguage', locale.languageCode),
                                            style: theme.textTheme.bodyMedium,
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            
                            // Sélecteur de langue
                            const Center(child: LanguageSelector()),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Bouton de déconnexion
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.error.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.logout,
                                    size: 24,
                                    color: theme.colorScheme.error,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ValueListenableBuilder<Locale>(
                                    valueListenable: _languageService.languageNotifier,
                                    builder: (context, locale, _) {
                                      return Text(
                                        _getLocalizedText('logout', locale.languageCode),
                                        style: theme.textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _logout,
                                icon: const Icon(Icons.logout),
                                label: ValueListenableBuilder<Locale>(
                                  valueListenable: _languageService.languageNotifier,
                                  builder: (context, locale, _) {
                                    return Text(_getLocalizedText('logout', locale.languageCode));
                                  },
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: theme.colorScheme.error,
                                  foregroundColor: theme.colorScheme.onError,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  String _getLocalizedText(String key, String languageCode) {
    final translations = {
      'fr': {
        'settings': 'Paramètres',
        'language': 'Langue',
        'chooseLanguage': 'Choisir la langue',
        'logout': 'Déconnexion',
        'languageChanged': 'Langue changée avec succès',
      },
      'ar': {
        'settings': 'الإعدادات',
        'language': 'اللغة',
        'chooseLanguage': 'اختر اللغة',
        'logout': 'تسجيل الخروج',
        'languageChanged': 'تم تغيير اللغة بنجاح',
      },
    };
    
    return translations[languageCode]?[key] ?? translations['fr']?[key] ?? key;
  }

  Future<void> _logout() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await UnifiedAuthService().logout();
      
      if (!mounted) return;
      
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }
}
