import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_api_service.dart';

import 'package:seatrace/models/admin.dart';
import 'package:seatrace/screens/login_screen.dart';
import 'package:seatrace/screens/admin/pending_users_screen.dart';
import 'package:seatrace/screens/admin/users_management_screen.dart';
import 'package:seatrace/screens/admin/statistics_screen.dart';
import 'package:seatrace/screens/admin/system_settings_screen.dart';
import 'package:seatrace/screens/profile_screen_new.dart';
import 'package:seatrace/screens/notifications_screen.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/utils/navigation_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/widgets/sea_widgets.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  String _userName = '';
  String _userPhoto = '';
  String _userEmail = '';

  int _pendingUsers = 0;
  int _totalUsers = 0;
  int _blockedUsers = 0;
  int _totalLots = 0;
  bool _isLoading = true;
  String? _errorMessage;
  List<Map<String, dynamic>> _recentActivities = [];

  final _animationService = AnimationService();
  final _responsiveService = ResponsiveService();
  final _navigationService = NavigationService();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Récupérer l'utilisateur connecté
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Vérifier que l'utilisateur est bien un administrateur
      if (user is! Admin) {
        throw Exception('L\'utilisateur n\'est pas un administrateur');
      }

      // Log pour déboguer
      ErrorHandler.instance.logInfo(
        'Utilisateur récupéré: id=${user.id}, nom=${user.nom}, prenom=${user.prenom}',
        context: 'AdminDashboardScreen._loadUserData',
      );

      // Charger les statistiques
      final pendingUsersResponse = await UnifiedApiService().get(
        'admins/pending-users',
      );
      final pendingUsersData = pendingUsersResponse['data'];
      final pendingUsers =
          pendingUsersData is List ? pendingUsersData : <dynamic>[];

      // Simuler d'autres statistiques pour le moment
      // Dans une version réelle, ces données viendraient de l'API
      final stats = {'totalUsers': 120, 'blockedUsers': 5, 'totalLots': 250};

      // Simuler des activités récentes
      final recentActivities = [
        {
          'type': 'validation',
          'message': 'Compte de pêcheur validé',
          'user': 'Ahmed Benali',
          'date': DateTime.now().subtract(const Duration(hours: 2)),
        },
        {
          'type': 'block',
          'message': 'Compte de client bloqué',
          'user': 'Marie Dupont',
          'date': DateTime.now().subtract(const Duration(hours: 5)),
        },
        {
          'type': 'validation',
          'message': 'Compte de vétérinaire validé',
          'user': 'Dr. Karim Hadj',
          'date': DateTime.now().subtract(const Duration(days: 1)),
        },
        {
          'type': 'system',
          'message': 'Mise à jour du système',
          'user': 'Système',
          'date': DateTime.now().subtract(const Duration(days: 2)),
        },
      ];

      setState(() {
        _userName = '${user.prenom} ${user.nom}';
        _userPhoto = user.photo ?? '';
        _userEmail = user.email;

        _pendingUsers = pendingUsers.length;
        _totalUsers = stats['totalUsers'] ?? 0;
        _blockedUsers = stats['blockedUsers'] ?? 0;
        _totalLots = stats['totalLots'] ?? 0;
        _recentActivities =
            recentActivities
                .map((activity) => activity as Map<String, dynamic>)
                .toList();
        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'AdminDashboardScreen._loadUserData',
      );
      setState(() {
        _errorMessage =
            'Erreur lors du chargement des données: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('Erreur', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Une erreur inconnue est survenue',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadUserData,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _logout() async {
    await UnifiedAuthService().logout();
    if (!mounted) return;
    _navigationService.replaceAllWithFade(context, const LoginScreen());
  }

  String _getInitials() {
    if (_userName.isEmpty) return '?';

    final nameParts = _userName.split(' ');
    String initials = '';

    if (nameParts.isNotEmpty && nameParts[0].isNotEmpty) {
      initials += nameParts[0][0];
    }

    if (nameParts.length > 1 && nameParts[1].isNotEmpty) {
      initials += nameParts[1][0];
    }

    return initials;
  }

  @override
  Widget build(BuildContext context) {
    final isPhone = _responsiveService.isPhone(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Administration'),
        elevation: 0,
        actions: [
          // Badge de notification
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: NotificationBadge(
              icon: Icons.notifications,
              iconSize: 24,
              badgeColor: Theme.of(context).colorScheme.error,
              onTap: () {
                _navigationService.navigateToWithSlideLeft(
                  context,
                  const NotificationsScreen(),
                );
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              _navigationService.navigateToWithFade(
                context,
                const ProfileScreenNew(),
              );
            },
            tooltip: 'Profil',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Déconnexion',
          ),
        ],
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? _buildErrorView()
                : RefreshIndicator(
                  onRefresh: _loadUserData,
                  child: SingleChildScrollView(
                    padding: _responsiveService.adaptivePadding(context),
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _animationService.staggeredList([
                        // En-tête avec informations utilisateur
                        _buildWelcomeCard(),

                        // Cartes d'action principales
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Actions',
                          icon: Icons.touch_app,
                        ),
                        _buildActionCards(isPhone),

                        // Statistiques
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Statistiques',
                          icon: Icons.bar_chart,
                        ),
                        _buildStatisticsRow(),

                        // Activité récente
                        const SizedBox(height: 24),
                        SeaSectionHeader(
                          title: 'Activité récente',
                          icon: Icons.history,
                          actionText: 'Voir tout',
                          onActionPressed: () {
                            // Navigation vers l'écran d'historique complet
                          },
                        ),
                        _buildRecentActivityList(),
                      ]),
                    ),
                  ),
                ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    final theme = Theme.of(context);

    return SeaCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Photo de profil ou initiales
              SeaAvatar(
                imageUrl: _userPhoto,
                initials: _getInitials(),
                size: 60,
                backgroundColor: theme.primaryColor.withValues(alpha: 0.1),
                foregroundColor: theme.primaryColor,
              ),
              const SizedBox(width: 16),
              // Informations utilisateur
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bienvenue, $_userName',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Administrateur',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.secondary,
                      ),
                    ),
                    if (_userEmail.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(_userEmail, style: theme.textTheme.bodySmall),
                    ],
                  ],
                ),
              ),
            ],
          ),

          // Utilisateurs en attente
          const SizedBox(height: 12),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.people_alt, size: 16, color: Colors.orange),
              const SizedBox(width: 4),
              Text(
                'Il y a $_pendingUsers utilisateurs en attente de validation',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCards(bool isPhone) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    if (isPhone) {
      return Column(
        children: [
          _buildActionCard(
            icon: Icons.people_alt,
            title: 'Utilisateurs en attente',
            description: 'Valider les nouveaux comptes',
            count: _pendingUsers,
            color: Colors.orange,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const PendingUsersScreen(),
              );
            },
          ),
          const SizedBox(height: 16),
          _buildActionCard(
            icon: Icons.manage_accounts,
            title: 'Gestion des utilisateurs',
            description: 'Gérer tous les utilisateurs',
            color: primaryColor,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const UsersManagementScreen(),
              );
            },
          ),
          const SizedBox(height: 16),
          _buildActionCard(
            icon: Icons.settings,
            title: 'Paramètres système',
            description: 'Configurer l\'application',
            color: theme.colorScheme.secondary,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const SystemSettingsScreen(),
              );
            },
          ),
        ],
      );
    } else {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  icon: Icons.people_alt,
                  title: 'Utilisateurs en attente',
                  description: 'Valider les nouveaux comptes',
                  count: _pendingUsers,
                  color: Colors.orange,
                  onTap: () {
                    _navigationService.navigateToWithSlideLeft(
                      context,
                      const PendingUsersScreen(),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildActionCard(
                  icon: Icons.manage_accounts,
                  title: 'Gestion des utilisateurs',
                  description: 'Gérer tous les utilisateurs',
                  color: primaryColor,
                  onTap: () {
                    _navigationService.navigateToWithSlideLeft(
                      context,
                      const UsersManagementScreen(),
                    );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  icon: Icons.settings,
                  title: 'Paramètres système',
                  description: 'Configurer l\'application',
                  color: theme.colorScheme.secondary,
                  onTap: () {
                    _navigationService.navigateToWithSlideLeft(
                      context,
                      const SystemSettingsScreen(),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildActionCard(
                  icon: Icons.bar_chart,
                  title: 'Statistiques',
                  description: 'Voir les statistiques globales',
                  color: Colors.blue,
                  onTap: () {
                    _navigationService.navigateToWithSlideLeft(
                      context,
                      const StatisticsScreen(),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      );
    }
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
    int? count,
  }) {
    return SeaCard(
      elevated: true,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  if (count != null) ...[
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        count.toString(),
                        style: TextStyle(
                          color: color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsRow() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          SeaStatCard(
            label: 'Utilisateurs en attente',
            value: _pendingUsers.toString(),
            icon: Icons.people_alt,
            color: Colors.orange,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const PendingUsersScreen(),
              );
            },
          ),
          const SizedBox(width: 12),
          SeaStatCard(
            label: 'Utilisateurs totaux',
            value: _totalUsers.toString(),
            icon: Icons.group,
            color: theme.primaryColor,
            onTap: () {
              _navigationService.navigateToWithSlideLeft(
                context,
                const UsersManagementScreen(),
              );
            },
          ),
          const SizedBox(width: 12),
          SeaStatCard(
            label: 'Utilisateurs bloqués',
            value: _blockedUsers.toString(),
            icon: Icons.block,
            color: theme.colorScheme.error,
          ),
          const SizedBox(width: 12),
          SeaStatCard(
            label: 'Lots totaux',
            value: _totalLots.toString(),
            icon: Icons.inventory_2,
            color: theme.colorScheme.secondary,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivityList() {
    final theme = Theme.of(context);

    if (_recentActivities.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('Aucune activité récente'),
        ),
      );
    }

    return Column(
      children:
          _recentActivities.map((activity) {
            final type = activity['type'] as String;
            final message = activity['message'] as String;
            final user = activity['user'] as String;
            final date = activity['date'] as DateTime;

            // Déterminer l'icône et la couleur en fonction du type d'activité
            IconData icon;
            Color color;

            switch (type) {
              case 'validation':
                icon = Icons.check_circle;
                color = theme.colorScheme.secondary;
                break;
              case 'block':
                icon = Icons.block;
                color = theme.colorScheme.error;
                break;
              case 'system':
                icon = Icons.settings;
                color = Colors.blue;
                break;
              default:
                icon = Icons.info;
                color = theme.primaryColor;
            }

            return SeaListItem(
              title: message,
              subtitle: '$user • ${_formatDate(date)}',
              icon: icon,
              backgroundColor: color.withValues(alpha: 0.05),
              onTap: () {
                // Afficher les détails de l'activité
              },
            );
          }).toList(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'il y a ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'il y a ${difference.inHours} heure${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'il y a ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'à l\'instant';
    }
  }
}
