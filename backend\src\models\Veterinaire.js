/**
 * <PERSON><PERSON><PERSON><PERSON> Veterinaire
 * Représente un vétérinaire dans l'application SeaTrace
 */

const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');

const veterinaireSchema = new mongoose.Schema({
  // Informations d'identification
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: [validator.isEmail, 'Email invalide']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  roles: {
    type: String,
    required: true,
    default: 'ROLE_VETERINAIRE'
  },

  // Informations personnelles
  nom: {
    type: String,
    required: true,
    trim: true
  },
  prenom: {
    type: String,
    required: true,
    trim: true
  },
  telephone: {
    type: String
  },
  photo: String,

  // Informations professionnelles
  cin: {
    type: String,
    required: true,
    unique: true
  },
  matricule: {
    type: String,
    required: true,
    unique: true
  },
  specialite: String,
  port: String,

  // Statut du compte
  isValidated: {
    type: Boolean,
    default: false
  },
  isBlocked: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(_, ret) {
      ret.id =  ret._id.toString(); //ret._id;_
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

/**
 * Middleware: Hash du mot de passe avant sauvegarde
 */
veterinaireSchema.pre('save', async function(next) {
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 8);
  }
  next();
});

/**
 * Méthode: Vérification du mot de passe
 */
veterinaireSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

/**
 * Méthode: Vérification de rôle
 */
veterinaireSchema.methods.hasRole = function(role) {
  return this.roles.includes(role);
};

/**
 * Méthode: Obtention du type d'utilisateur
 */
veterinaireSchema.methods.getUserType = function() {
  return 'veterinaire';
};

const Veterinaire = mongoose.model('Veterinaire', veterinaireSchema);

module.exports = Veterinaire;
