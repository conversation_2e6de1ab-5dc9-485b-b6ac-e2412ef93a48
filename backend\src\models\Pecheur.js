/**
 * <PERSON><PERSON><PERSON><PERSON>eur
 * Représente un pêcheur dans l'application SeaTrace
 */

const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');

const pecheurSchema = new mongoose.Schema({
  // Informations d'identification
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: [validator.isEmail, 'Email invalide']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  roles: {
    type: String,
    required: true,
    default: 'ROLE_PECHEUR'
  },

  // Informations personnelles
  nom: {
    type: String,
    required: true,
    trim: true
  },
  prenom: {
    type: String,
    required: true,
    trim: true
  },
  telephone: {
    type: String,
    required: false // Optionnel pour correspondre au frontend
  },
  photo: String,

  // Informations professionnelles
  cin: {
    type: String,
    required: true,
    unique: true
  },
  matricule: {
    type: String,
    required: true,
    unique: true
  },

  // Informations sur le bateau (champs essentiels uniquement)
  capacite: String,
  longueur: String, // Seul champ conservé pour la longueur
  largeur: String,
  bateau: String,
  port: String,
  engin: String,

  // Statut du compte
  isValidated: {
    type: Boolean,
    default: false
  },
  isBlocked: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(_, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

/**
 * Middleware: Hash du mot de passe avant sauvegarde
 */
pecheurSchema.pre('save', async function(next) {
  // Hash du mot de passe
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 8);
  }

  next();
});

/**
 * Méthode: Vérification du mot de passe
 */
pecheurSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

/**
 * Méthode: Vérification de rôle
 */
pecheurSchema.methods.hasRole = function(role) {
  return this.roles.includes(role);
};

/**
 * Méthode: Obtention du type d'utilisateur
 */
pecheurSchema.methods.getUserType = function() {
  return 'pecheur';
};

const Pecheur = mongoose.model('Pecheur', pecheurSchema);

module.exports = Pecheur;