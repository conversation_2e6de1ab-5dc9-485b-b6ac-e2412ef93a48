import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('fr')
  ];

  /// No description provided for @appTitle.
  ///
  /// In fr, this message translates to:
  /// **'SeaTrace'**
  String get appTitle;

  /// No description provided for @loginTitle.
  ///
  /// In fr, this message translates to:
  /// **'Connexion'**
  String get loginTitle;

  /// No description provided for @email.
  ///
  /// In fr, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In fr, this message translates to:
  /// **'Mot de passe'**
  String get password;

  /// No description provided for @login.
  ///
  /// In fr, this message translates to:
  /// **'Se connecter'**
  String get login;

  /// No description provided for @register.
  ///
  /// In fr, this message translates to:
  /// **'S\'inscrire'**
  String get register;

  /// No description provided for @forgotPassword.
  ///
  /// In fr, this message translates to:
  /// **'Mot de passe oublié ?'**
  String get forgotPassword;

  /// No description provided for @serverIp.
  ///
  /// In fr, this message translates to:
  /// **'Adresse IP du serveur'**
  String get serverIp;

  /// No description provided for @testConnection.
  ///
  /// In fr, this message translates to:
  /// **'Tester la connexion'**
  String get testConnection;

  /// No description provided for @applyIp.
  ///
  /// In fr, this message translates to:
  /// **'Appliquer'**
  String get applyIp;

  /// No description provided for @discoverServers.
  ///
  /// In fr, this message translates to:
  /// **'Découvrir les serveurs'**
  String get discoverServers;

  /// No description provided for @welcomeBack.
  ///
  /// In fr, this message translates to:
  /// **'Bienvenue, {name}'**
  String welcomeBack(Object name);

  /// No description provided for @dashboard.
  ///
  /// In fr, this message translates to:
  /// **'Tableau de bord'**
  String get dashboard;

  /// No description provided for @profile.
  ///
  /// In fr, this message translates to:
  /// **'Profil'**
  String get profile;

  /// No description provided for @settings.
  ///
  /// In fr, this message translates to:
  /// **'Paramètres'**
  String get settings;

  /// No description provided for @logout.
  ///
  /// In fr, this message translates to:
  /// **'Déconnexion'**
  String get logout;

  /// No description provided for @notifications.
  ///
  /// In fr, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @noNotifications.
  ///
  /// In fr, this message translates to:
  /// **'Aucune notification'**
  String get noNotifications;

  /// No description provided for @markAllAsRead.
  ///
  /// In fr, this message translates to:
  /// **'Marquer tout comme lu'**
  String get markAllAsRead;

  /// No description provided for @language.
  ///
  /// In fr, this message translates to:
  /// **'Langue'**
  String get language;

  /// No description provided for @french.
  ///
  /// In fr, this message translates to:
  /// **'Français'**
  String get french;

  /// No description provided for @arabic.
  ///
  /// In fr, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// No description provided for @chooseLanguage.
  ///
  /// In fr, this message translates to:
  /// **'Choisir la langue'**
  String get chooseLanguage;

  /// No description provided for @save.
  ///
  /// In fr, this message translates to:
  /// **'Enregistrer'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In fr, this message translates to:
  /// **'Annuler'**
  String get cancel;

  /// No description provided for @error.
  ///
  /// In fr, this message translates to:
  /// **'Erreur'**
  String get error;

  /// No description provided for @success.
  ///
  /// In fr, this message translates to:
  /// **'Succès'**
  String get success;

  /// No description provided for @warning.
  ///
  /// In fr, this message translates to:
  /// **'Avertissement'**
  String get warning;

  /// No description provided for @info.
  ///
  /// In fr, this message translates to:
  /// **'Information'**
  String get info;

  /// No description provided for @loading.
  ///
  /// In fr, this message translates to:
  /// **'Chargement...'**
  String get loading;

  /// No description provided for @retry.
  ///
  /// In fr, this message translates to:
  /// **'Réessayer'**
  String get retry;

  /// No description provided for @noData.
  ///
  /// In fr, this message translates to:
  /// **'Aucune donnée disponible'**
  String get noData;

  /// No description provided for @scanFish.
  ///
  /// In fr, this message translates to:
  /// **'Scanner un poisson'**
  String get scanFish;

  /// No description provided for @fishDetails.
  ///
  /// In fr, this message translates to:
  /// **'Détails du poisson'**
  String get fishDetails;

  /// No description provided for @weight.
  ///
  /// In fr, this message translates to:
  /// **'Poids'**
  String get weight;

  /// No description provided for @temperature.
  ///
  /// In fr, this message translates to:
  /// **'Température'**
  String get temperature;

  /// No description provided for @fishingMethod.
  ///
  /// In fr, this message translates to:
  /// **'Méthode de pêche'**
  String get fishingMethod;

  /// No description provided for @fishingZone.
  ///
  /// In fr, this message translates to:
  /// **'Zone de pêche'**
  String get fishingZone;

  /// No description provided for @location.
  ///
  /// In fr, this message translates to:
  /// **'Emplacement'**
  String get location;

  /// No description provided for @selectMareyeur.
  ///
  /// In fr, this message translates to:
  /// **'Sélectionner un mareyeur'**
  String get selectMareyeur;

  /// No description provided for @selectVeterinarian.
  ///
  /// In fr, this message translates to:
  /// **'Sélectionner un vétérinaire'**
  String get selectVeterinarian;

  /// No description provided for @submit.
  ///
  /// In fr, this message translates to:
  /// **'Soumettre'**
  String get submit;

  /// No description provided for @pendingLots.
  ///
  /// In fr, this message translates to:
  /// **'Lots en attente'**
  String get pendingLots;

  /// No description provided for @approvedLots.
  ///
  /// In fr, this message translates to:
  /// **'Lots approuvés'**
  String get approvedLots;

  /// No description provided for @rejectedLots.
  ///
  /// In fr, this message translates to:
  /// **'Lots refusés'**
  String get rejectedLots;

  /// No description provided for @viewPendingLots.
  ///
  /// In fr, this message translates to:
  /// **'Voir les lots en attente'**
  String get viewPendingLots;

  /// No description provided for @statistics.
  ///
  /// In fr, this message translates to:
  /// **'Statistiques'**
  String get statistics;

  /// No description provided for @recentActivity.
  ///
  /// In fr, this message translates to:
  /// **'Activité récente'**
  String get recentActivity;

  /// No description provided for @viewAll.
  ///
  /// In fr, this message translates to:
  /// **'Voir tout'**
  String get viewAll;

  /// No description provided for @noRecentActivity.
  ///
  /// In fr, this message translates to:
  /// **'Aucune activité récente'**
  String get noRecentActivity;

  /// No description provided for @approve.
  ///
  /// In fr, this message translates to:
  /// **'Approuver'**
  String get approve;

  /// No description provided for @reject.
  ///
  /// In fr, this message translates to:
  /// **'Rejeter'**
  String get reject;

  /// No description provided for @rejectionReason.
  ///
  /// In fr, this message translates to:
  /// **'Motif de rejet'**
  String get rejectionReason;

  /// No description provided for @initialPrice.
  ///
  /// In fr, this message translates to:
  /// **'Prix initial'**
  String get initialPrice;

  /// No description provided for @minimalPrice.
  ///
  /// In fr, this message translates to:
  /// **'Prix minimal'**
  String get minimalPrice;

  /// No description provided for @startAuction.
  ///
  /// In fr, this message translates to:
  /// **'Démarrer l\'enchère'**
  String get startAuction;

  /// No description provided for @activeAuctions.
  ///
  /// In fr, this message translates to:
  /// **'Enchères actives'**
  String get activeAuctions;

  /// No description provided for @completedAuctions.
  ///
  /// In fr, this message translates to:
  /// **'Enchères terminées'**
  String get completedAuctions;

  /// No description provided for @availableAuctions.
  ///
  /// In fr, this message translates to:
  /// **'Enchères disponibles'**
  String get availableAuctions;

  /// No description provided for @myPurchases.
  ///
  /// In fr, this message translates to:
  /// **'Mes achats'**
  String get myPurchases;

  /// No description provided for @bid.
  ///
  /// In fr, this message translates to:
  /// **'Enchérir'**
  String get bid;

  /// No description provided for @currentBid.
  ///
  /// In fr, this message translates to:
  /// **'Enchère actuelle'**
  String get currentBid;

  /// No description provided for @timeRemaining.
  ///
  /// In fr, this message translates to:
  /// **'Temps restant'**
  String get timeRemaining;

  /// No description provided for @extendAuction.
  ///
  /// In fr, this message translates to:
  /// **'Prolonger l\'enchère'**
  String get extendAuction;

  /// No description provided for @closeAuction.
  ///
  /// In fr, this message translates to:
  /// **'Clôturer l\'enchère'**
  String get closeAuction;

  /// No description provided for @auctionClosed.
  ///
  /// In fr, this message translates to:
  /// **'Enchère clôturée'**
  String get auctionClosed;

  /// No description provided for @youWon.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez remporté cette enchère !'**
  String get youWon;

  /// No description provided for @confirmSpecies.
  ///
  /// In fr, this message translates to:
  /// **'Confirmer cette espèce'**
  String get confirmSpecies;

  /// No description provided for @refreshCounters.
  ///
  /// In fr, this message translates to:
  /// **'Rafraîchir les compteurs'**
  String get refreshCounters;

  /// No description provided for @countersRefreshed.
  ///
  /// In fr, this message translates to:
  /// **'Compteurs rafraîchis avec succès'**
  String get countersRefreshed;

  /// No description provided for @changeLanguage.
  ///
  /// In fr, this message translates to:
  /// **'Changer de langue'**
  String get changeLanguage;

  /// No description provided for @languageChanged.
  ///
  /// In fr, this message translates to:
  /// **'Langue changée avec succès'**
  String get languageChanged;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
    case 'fr': return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
