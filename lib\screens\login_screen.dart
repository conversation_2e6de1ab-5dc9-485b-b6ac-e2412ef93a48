import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/service_config.dart';
import 'package:seatrace/services/network_discovery_service.dart';
import 'package:seatrace/services/language_service.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/client.dart';
import 'package:seatrace/models/admin.dart';
import 'package:seatrace/screens/pecheur/dashboard_screen.dart';
import 'package:seatrace/screens/veterinaire/dashboard_screen.dart';
import 'package:seatrace/screens/maryeur/dashboard_screen.dart';
import 'package:seatrace/screens/client/dashboard_screen.dart';
import 'package:seatrace/screens/admin/dashboard_screen.dart';
import 'package:seatrace/screens/signup_screen.dart';
import 'package:seatrace/utils/validators.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/utils/app_error.dart';
import 'package:seatrace/widgets/error_display.dart';
import 'package:seatrace/widgets/sea_widgets.dart';
import 'package:seatrace/widgets/language_selector.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => LoginScreenState();
}

class LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _serverIpController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;

  // Service de découverte réseau
  final _discoveryService = NetworkDiscoveryService();

  // Liste des serveurs découverts
  List<String> _discoveredServers = [];

  // État de la découverte
  bool _isDiscovering = false;
  String? _discoveryStatus;

  @override
  void initState() {
    super.initState();
    _loadSavedServerIp();
    _loadDiscoveredServers();
  }

  /// Charger les serveurs précédemment découverts
  Future<void> _loadDiscoveredServers() async {
    try {
      final servers = await _discoveryService.loadDiscoveredServers();
      setState(() {
        _discoveredServers = servers;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'LoginScreen._loadDiscoveredServers',
      );
    }
  }

  Future<void> _loadSavedServerIp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedIp = prefs.getString('working_server_ip');
      if (savedIp != null && savedIp.isNotEmpty) {
        setState(() {
          _serverIpController.text = savedIp;
        });
      } else {
        // Utiliser l'adresse IP par défaut
        _serverIpController.text = '*************';
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'LoginScreen._loadSavedServerIp',
      );
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _serverIpController.dispose();
    super.dispose();
  }

  /// Teste la connexion au serveur et affiche le résultat
  Future<void> _testServerConnection() async {
    setState(() {
      _isLoading = true;
      _errorMessage = 'Test de connexion en cours...';
    });

    try {
      // Récupérer les URLs pour les afficher
      final apiUrl = ServiceConfig().apiBaseUrl;
      final baseUrl = ServiceConfig.getBaseUrl();

      // Journaliser les URLs pour le débogage
      ErrorHandler.instance.logInfo(
        'Test de connexion avec URL de base: $baseUrl et URL API: $apiUrl',
        context: 'LoginScreen._testServerConnection',
      );

      // Tester la connexion avec l'adresse IP Wi-Fi
      final apiService = UnifiedApiService();
      bool isConnected = false;
      String successUrl = '';

      // Essayer d'abord avec l'adresse IP personnalisée si disponible
      if (_serverIpController.text.isNotEmpty) {
        final customIp = _serverIpController.text;
        final testUrl = 'http://$customIp:3005';
        apiService.setBaseUrl(testUrl);

        ErrorHandler.instance.logInfo(
          'Test de connexion avec IP personnalisée: $testUrl',
          context: 'LoginScreen._testServerConnection',
        );

        isConnected = await apiService.checkServerConnectivity();
        if (isConnected) {
          successUrl = testUrl;

          // Sauvegarder l'adresse IP qui fonctionne
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('working_server_ip', customIp);
        }
      }

      // Si l'adresse personnalisée ne fonctionne pas, essayer avec les serveurs découverts
      if (!isConnected && _discoveredServers.isNotEmpty) {
        for (final ip in _discoveredServers) {
          final testUrl = 'http://$ip:3005';
          apiService.setBaseUrl(testUrl);

          ErrorHandler.instance.logInfo(
            'Test de connexion avec serveur découvert: $testUrl',
            context: 'LoginScreen._testServerConnection',
          );

          isConnected = await apiService.checkServerConnectivity();
          if (isConnected) {
            successUrl = testUrl;

            // Sauvegarder l'adresse IP qui fonctionne
            final prefs = await SharedPreferences.getInstance();
            await prefs.setString('working_server_ip', ip);

            // Mettre à jour le champ d'adresse IP
            _serverIpController.text = ip;
            break;
          }
        }
      }

      // Si aucun serveur découvert ne fonctionne, essayer avec l'adresse IP Wi-Fi
      if (!isConnected) {
        // Utiliser l'adresse IP Wi-Fi
        final ip = '*************'; // Adresse IP Wi-Fi
        final testUrl = 'http://$ip:3005';
        apiService.setBaseUrl(testUrl);

        ErrorHandler.instance.logInfo(
          'Test de connexion avec adresse IP Wi-Fi: $testUrl',
          context: 'LoginScreen._testServerConnection',
        );

        isConnected = await apiService.checkServerConnectivity();
        if (isConnected) {
          successUrl = testUrl;

          // Sauvegarder l'adresse IP qui fonctionne
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('working_server_ip', ip);

          // Mettre à jour le champ d'adresse IP
          _serverIpController.text = ip;
        }
      }

      setState(() {
        if (isConnected) {
          _errorMessage =
              'Connexion au serveur réussie! Le serveur est accessible.\n'
              'URL fonctionnelle: $successUrl\n'
              'L\'application utilisera maintenant cette URL pour les connexions.';
        } else {
          _errorMessage =
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.\n\n'
              'URLs testées: \n- ${_serverIpController.text}:3005\n- Serveurs découverts: ${_discoveredServers.isEmpty ? "aucun" : _discoveredServers.join(", ")}\n- *************:3005 (Wi-Fi)\n\n'
              'Conseils de dépannage:\n'
              '1. Vérifiez que le serveur backend est démarré (vous devriez voir "WebSocket démarré sur..." dans la console)\n'
              '2. Assurez-vous que votre téléphone et l\'ordinateur sont connectés au même réseau Wi-Fi\n'
              '3. Vérifiez que le port 3005 n\'est pas bloqué par un pare-feu\n'
              '4. Essayez d\'utiliser la découverte automatique du serveur\n'
              '5. Essayez d\'entrer manuellement l\'adresse IP de votre ordinateur dans le champ ci-dessous';
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du test de connexion: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// Applique l'adresse IP du serveur personnalisée
  Future<void> _applyCustomServerIp() async {
    if (_serverIpController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Veuillez entrer une adresse IP valide';
      });
      return;
    }

    try {
      final customIp = _serverIpController.text;
      final testUrl = 'http://$customIp:3005';

      // Mettre à jour l'URL de base
      final apiService = UnifiedApiService();
      apiService.setBaseUrl(testUrl);

      // Sauvegarder l'adresse IP
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('working_server_ip', customIp);

      // Tester la connexion
      _testServerConnection();
    } catch (e) {
      setState(() {
        _errorMessage =
            'Erreur lors de l\'application de l\'adresse IP: ${e.toString()}';
      });
    }
  }

  /// Lancer la découverte automatique des serveurs
  Future<void> _discoverServers() async {
    setState(() {
      _isDiscovering = true;
      _discoveryStatus = 'Démarrage de la découverte des serveurs...';
      _errorMessage = null;
    });

    try {
      // Fonction de callback pour mettre à jour le statut
      void updateStatus(String status) {
        if (mounted) {
          setState(() {
            _discoveryStatus = status;
          });
        }
      }

      // Lancer la découverte
      final discoveredServers = await _discoveryService.discoverServers(
        onProgress: updateStatus,
        saveDiscovered: true,
      );

      if (mounted) {
        setState(() {
          _isDiscovering = false;
          _discoveredServers = discoveredServers;

          if (discoveredServers.isEmpty) {
            _discoveryStatus = 'Aucun serveur trouvé sur le réseau local.';
          } else {
            _discoveryStatus =
                'Serveurs trouvés: ${discoveredServers.join(', ')}';

            // Utiliser le premier serveur découvert
            if (_serverIpController.text.isEmpty) {
              _serverIpController.text = discoveredServers.first;
            }
          }
        });

        // Tester la connexion avec les serveurs découverts
        if (discoveredServers.isNotEmpty) {
          _testServerConnection();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDiscovering = false;
          _discoveryStatus = 'Erreur lors de la découverte: ${e.toString()}';
        });
      }

      ErrorHandler.instance.logError(
        e,
        context: 'LoginScreen._discoverServers',
      );
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await UnifiedAuthService().login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (user == null) {
        setState(() {
          _errorMessage = 'Email ou mot de passe incorrect';
          _isLoading = false;
        });
        return;
      }

      // Navigate to the appropriate dashboard based on user role
      if (!mounted) return;

      if (user is Pecheur) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const PecheurDashboardScreen()),
        );
      } else if (user is Veterinaire) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const VeterinaireDashboardScreen()),
        );
      } else if (user is Maryeur) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const MaryeurDashboardScreen()),
        );
      } else if (user is Client) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const ClientDashboardScreen()),
        );
      } else if (user is Admin) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const AdminDashboardScreen()),
        );
      } else {
        setState(() {
          _errorMessage = 'Rôle utilisateur non reconnu';
          _isLoading = false;
        });
      }
    } on AppError catch (e) {
      // Utiliser le message d'erreur convivial de AppError
      setState(() {
        _errorMessage = e.message;
        _isLoading = false;
      });

      // Journaliser l'erreur
      ErrorHandler.instance.logError(e, context: 'LoginScreen._login');
    } catch (e) {
      // Créer une AppError pour les autres types d'erreurs
      final appError = ErrorHandler.instance.handleException(
        e,
        context: 'LoginScreen._login',
      );

      setState(() {
        _errorMessage = appError.message;
        _isLoading = false;
      });

      // Journaliser l'erreur
      ErrorHandler.instance.logError(appError);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).primaryColor;
    final surfaceColor = Theme.of(context).colorScheme.surface;

    return Scaffold(
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors:
                    isDarkMode
                        ? [const Color(0xFF0F0F1A), const Color(0xFF1A1A2E)]
                        : [const Color(0xFFF8F9FA), const Color(0xFFE1F5FE)],
              ),
            ),
          ),

          // Wave pattern decoration
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Opacity(
              opacity: 0.1,
              child: Image.network(
                'https://raw.githubusercontent.com/flutter/website/main/examples/layout/lakes/step5/images/lake.jpg',
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => const SizedBox(),
              ),
            ),
          ),

          // Bouton de changement de langue en haut à droite
          const Positioned(top: 50, right: 20, child: LanguageSelector()),

          // Main content
          SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                  vertical: 16.0,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Logo and app name
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: surfaceColor,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Logo
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: primaryColor.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.sailing,
                              size: 64,
                              color: primaryColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // App name
                          Text(
                            'SeaTrace',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: primaryColor,
                              letterSpacing: 1.2,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Tagline avec traduction
                          ValueListenableBuilder<Locale>(
                            valueListenable: LanguageService().languageNotifier,
                            builder: (context, locale, _) {
                              return Text(
                                _getLocalizedText(
                                  'tagline',
                                  locale.languageCode,
                                ),
                                textAlign: TextAlign.center,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyLarge?.copyWith(
                                  color:
                                      Theme.of(
                                        context,
                                      ).textTheme.bodySmall?.color,
                                  fontStyle: FontStyle.italic,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Login form
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: surfaceColor,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            ValueListenableBuilder<Locale>(
                              valueListenable:
                                  LanguageService().languageNotifier,
                              builder: (context, locale, _) {
                                return Text(
                                  _getLocalizedText(
                                    'login',
                                    locale.languageCode,
                                  ),
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                );
                              },
                            ),
                            const SizedBox(height: 24),

                            // Email field
                            ValueListenableBuilder<Locale>(
                              valueListenable:
                                  LanguageService().languageNotifier,
                              builder: (context, locale, _) {
                                return TextFormField(
                                  controller: _emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  decoration: InputDecoration(
                                    labelText: _getLocalizedText(
                                      'email',
                                      locale.languageCode,
                                    ),
                                    hintText:
                                        locale.languageCode == 'ar'
                                            ? 'أدخل عنوان بريدك الإلكتروني'
                                            : 'Entrez votre adresse email',
                                    prefixIcon: const Icon(
                                      Icons.email_outlined,
                                    ),
                                  ),
                                  validator: Validators.validateEmail,
                                );
                              },
                            ),
                            const SizedBox(height: 16),

                            // Password field
                            ValueListenableBuilder<Locale>(
                              valueListenable:
                                  LanguageService().languageNotifier,
                              builder: (context, locale, _) {
                                return TextFormField(
                                  controller: _passwordController,
                                  obscureText: _obscurePassword,
                                  decoration: InputDecoration(
                                    labelText: _getLocalizedText(
                                      'password',
                                      locale.languageCode,
                                    ),
                                    hintText:
                                        locale.languageCode == 'ar'
                                            ? 'أدخل كلمة المرور'
                                            : 'Entrez votre mot de passe',
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _obscurePassword
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _obscurePassword = !_obscurePassword;
                                        });
                                      },
                                    ),
                                  ),
                                  validator: Validators.validatePassword,
                                );
                              },
                            ),

                            // Error message
                            if (_errorMessage != null) ...[
                              const SizedBox(height: 16),
                              FormErrorDisplay(message: _errorMessage),
                              const SizedBox(height: 8),
                              if (_errorMessage!.contains('trop de temps') ||
                                  _errorMessage!.contains('connexion') ||
                                  _errorMessage!.contains('internet') ||
                                  _errorMessage!.contains('serveur')) ...[
                                Text(
                                  'Conseils de dépannage:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '• Vérifiez votre connexion internet\n'
                                  '• Assurez-vous que le serveur est en cours d\'exécution\n'
                                  '• Vérifiez que votre téléphone et le serveur sont sur le même réseau Wi-Fi\n'
                                  '• Essayez de vous connecter plus tard',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // Configuration de l'adresse IP du serveur
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withValues(alpha: 128),
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Configuration du serveur',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: TextFormField(
                                              controller: _serverIpController,
                                              decoration: const InputDecoration(
                                                labelText:
                                                    'Adresse IP du serveur',
                                                hintText: 'Ex: *************',
                                                isDense: true,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          ElevatedButton(
                                            onPressed:
                                                _isLoading
                                                    ? null
                                                    : _applyCustomServerIp,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Theme.of(
                                                    context,
                                                  ).colorScheme.primary,
                                              foregroundColor:
                                                  Theme.of(
                                                    context,
                                                  ).colorScheme.onPrimary,
                                            ),
                                            child: const Text('Appliquer'),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Adresse actuelle: ${ServiceConfig.getBaseUrl()}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall?.color,
                                        ),
                                      ),

                                      if (_discoveryStatus != null) ...[
                                        const SizedBox(height: 8),
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primaryContainer
                                                .withValues(alpha: 77),
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Statut de la découverte:',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 12,
                                                  color:
                                                      Theme.of(
                                                        context,
                                                      ).colorScheme.primary,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                _discoveryStatus!,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color:
                                                      Theme.of(
                                                        context,
                                                      ).colorScheme.onSurface,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],

                                      if (_discoveredServers.isNotEmpty) ...[
                                        const SizedBox(height: 8),
                                        Text(
                                          'Serveurs découverts:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Container(
                                          height: 100,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color:
                                                  Theme.of(
                                                    context,
                                                  ).colorScheme.outline,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                          child: ListView.builder(
                                            shrinkWrap: true,
                                            itemCount:
                                                _discoveredServers.length,
                                            itemBuilder: (context, index) {
                                              final ip =
                                                  _discoveredServers[index];
                                              return ListTile(
                                                dense: true,
                                                title: Text(ip),
                                                trailing: IconButton(
                                                  icon: const Icon(
                                                    Icons.check_circle_outline,
                                                    size: 20,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _serverIpController.text =
                                                          ip;
                                                    });
                                                    _applyCustomServerIp();
                                                  },
                                                  tooltip:
                                                      'Utiliser ce serveur',
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ],

                                      const SizedBox(height: 8),
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton.icon(
                                          onPressed:
                                              _isDiscovering
                                                  ? null
                                                  : _discoverServers,
                                          icon: const Icon(Icons.search),
                                          label: Text(
                                            _isDiscovering
                                                ? 'Recherche en cours...'
                                                : 'Découvrir automatiquement',
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.secondary,
                                            foregroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.onSecondary,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _testServerConnection,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(
                                          context,
                                        ).colorScheme.errorContainer,
                                    foregroundColor:
                                        Theme.of(
                                          context,
                                        ).colorScheme.onErrorContainer,
                                  ),
                                  child: const Text(
                                    'Tester la connexion au serveur',
                                  ),
                                ),
                                const SizedBox(height: 8),
                                CustomButton.outline(
                                  text: 'Réessayer',
                                  onPressed: _isLoading ? null : _login,
                                  size: CustomButtonSize.small,
                                ),
                              ],
                            ],

                            const SizedBox(height: 24),

                            // Login button
                            CustomButton.filled(
                              text: 'Se connecter',
                              onPressed: _isLoading ? null : _login,
                              isLoading: _isLoading,
                              size: CustomButtonSize.large,
                              width: double.infinity,
                            ),

                            const SizedBox(height: 16),

                            // Sign up link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Vous n\'avez pas de compte?',
                                  style: TextStyle(
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.bodySmall?.color,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                CustomButton.text(
                                  text: 'Créer un compte',
                                  onPressed: () {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (_) => const SignupScreen(),
                                      ),
                                    );
                                  },
                                  size: CustomButtonSize.small,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getLocalizedText(String key, String languageCode) {
    final translations = {
      'fr': {
        'tagline':
            'De la mer à l\'assiette, la transparence est le meilleur enchérisseur',
        'subtitle': 'Le goût authentique de la Mer Sans intermédiaire',
        'login': 'Connexion',
        'email': 'Email',
        'password': 'Mot de passe',
        'loginButton': 'Se connecter',
        'noAccount': 'Vous n\'avez pas de compte?',
        'createAccount': 'Créer un compte',
        'serverConfig': 'Configuration du serveur',
        'serverIp': 'Adresse IP du serveur',
        'apply': 'Appliquer',
        'testConnection': 'Tester la connexion au serveur',
        'retry': 'Réessayer',
        'discoverServers': 'Découvrir automatiquement',
        'searching': 'Recherche en cours...',
      },
      'ar': {
        'tagline': 'من البحر إلى الطبق، الشفافية هي أفضل مزايد',
        'subtitle': 'الطعم الأصيل للبحر بدون وسطاء',
        'login': 'تسجيل الدخول',
        'email': 'البريد الإلكتروني',
        'password': 'كلمة المرور',
        'loginButton': 'تسجيل الدخول',
        'noAccount': 'ليس لديك حساب؟',
        'createAccount': 'إنشاء حساب',
        'serverConfig': 'إعدادات الخادم',
        'serverIp': 'عنوان IP للخادم',
        'apply': 'تطبيق',
        'testConnection': 'اختبار الاتصال بالخادم',
        'retry': 'إعادة المحاولة',
        'discoverServers': 'اكتشاف تلقائي',
        'searching': 'جاري البحث...',
      },
    };

    return translations[languageCode]?[key] ?? translations['fr']?[key] ?? key;
  }
}
