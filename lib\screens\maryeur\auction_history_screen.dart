import 'package:flutter/material.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:intl/intl.dart';

class MaryeurAuctionHistoryScreen extends StatefulWidget {
  const MaryeurAuctionHistoryScreen({super.key});

  @override
  State<MaryeurAuctionHistoryScreen> createState() =>
      _MaryeurAuctionHistoryScreenState();
}

class _MaryeurAuctionHistoryScreenState
    extends State<MaryeurAuctionHistoryScreen>
    with TickerProviderStateMixin {
  final UnifiedLotService _lotService = UnifiedLotService();
  final UnifiedAuthService _authService = UnifiedAuthService();

  List<Lot> _activeLots = [];
  List<Lot> _soldLots = [];
  bool _isLoading = true;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAuctionHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAuctionHistory() async {
    try {
      setState(() => _isLoading = true);

      final user = await _authService.getCurrentUser();
      if (user == null) return;

      // Charger les enchères actives
      final activeLots = await _lotService.getMaryeurActiveAuctions(user.id);

      // Charger les lots vendus
      final soldLots = await _lotService.getMaryeurSoldLots(user.id);

      setState(() {
        _activeLots = activeLots;
        _soldLots = soldLots;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = const Color(0xFF1565C0);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Historique des Enchères'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.gavel, size: 18),
                  const SizedBox(width: 8),
                  Text('Actives (${_activeLots.length})'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check_circle, size: 18),
                  const SizedBox(width: 8),
                  Text('Vendues (${_soldLots.length})'),
                ],
              ),
            ),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [_buildActiveAuctionsTab(), _buildSoldLotsTab()],
              ),
    );
  }

  Widget _buildActiveAuctionsTab() {
    if (_activeLots.isEmpty) {
      return _buildEmptyState(
        icon: Icons.gavel,
        title: 'Aucune enchère active',
        subtitle: 'Vos enchères en cours apparaîtront ici',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAuctionHistory,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _activeLots.length,
        itemBuilder: (context, index) {
          return _buildActiveLotCard(_activeLots[index]);
        },
      ),
    );
  }

  Widget _buildSoldLotsTab() {
    if (_soldLots.isEmpty) {
      return _buildEmptyState(
        icon: Icons.shopping_cart,
        title: 'Aucun lot vendu',
        subtitle: 'Vos lots vendus apparaîtront ici',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAuctionHistory,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _soldLots.length,
        itemBuilder: (context, index) {
          return _buildSoldLotCard(_soldLots[index]);
        },
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(subtitle, style: TextStyle(color: Colors.grey[500])),
        ],
      ),
    );
  }

  Widget _buildActiveLotCard(Lot lot) {
    final theme = Theme.of(context);
    final primaryColor = const Color(0xFF1565C0);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showLotDetails(lot),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.gavel, size: 16, color: Colors.orange[700]),
                        const SizedBox(width: 4),
                        Text(
                          'Enchère Active',
                          style: TextStyle(
                            color: Colors.orange[700],
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    lot.identifiant,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lot.especeNom,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: primaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${lot.poids} kg',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Prix minimal: ${lot.prixMinimal} DT',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (lot.photo != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        lot.photo!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Lancée le ${lot.dateSoumission != null ? DateFormat('dd/MM/yyyy à HH:mm').format(lot.dateSoumission!) : 'Date inconnue'}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSoldLotCard(Lot lot) {
    final theme = Theme.of(context);
    final primaryColor = const Color(0xFF1565C0);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showLotDetails(lot),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: Colors.green[700],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Vendu',
                          style: TextStyle(
                            color: Colors.green[700],
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    lot.identifiant,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lot.especeNom,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: primaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${lot.poids} kg',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'Prix final: ',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[700],
                              ),
                            ),
                            Text(
                              '${lot.prixFinal} DT',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.green[700],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (lot.photo != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        lot.photo!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Acheteur: ${lot.acheteur ?? 'Non spécifié'}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLotDetails(Lot lot) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails du Lot ${lot.identifiant}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow('Espèce', lot.especeNom),
                  _buildDetailRow('Poids', '${lot.poids} kg'),
                  _buildDetailRow('Température', '${lot.temperature}°C'),
                  _buildDetailRow('Lieu', lot.lieu ?? 'Non spécifié'),
                  if (lot.prixMinimal != null)
                    _buildDetailRow('Prix minimal', '${lot.prixMinimal} DT'),
                  if (lot.prixFinal != null)
                    _buildDetailRow('Prix final', '${lot.prixFinal} DT'),
                  if (lot.acheteur != null)
                    _buildDetailRow('Acheteur', lot.acheteur!),
                  _buildDetailRow(
                    'Date soumission',
                    lot.dateSoumission != null
                        ? DateFormat(
                          'dd/MM/yyyy à HH:mm',
                        ).format(lot.dateSoumission!)
                        : 'Date inconnue',
                  ),
                  _buildDetailRow('Statut', lot.vendu ? 'Vendu' : 'En enchère'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
