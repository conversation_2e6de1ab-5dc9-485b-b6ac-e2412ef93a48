import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:seatrace/l10n/l10n.dart';
import 'package:seatrace/utils/error_handler.dart';

/// Service pour gérer les langues de l'application
class LanguageService {
  // Singleton
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  // Clé pour stocker la langue dans les préférences
  static const String _languageKey = 'app_language';

  // Contrôleur pour notifier les changements de langue
  final _languageController = ValueNotifier<Locale>(const Locale('fr'));

  // Getter pour le contrôleur
  ValueNotifier<Locale> get languageNotifier => _languageController;

  // Getter pour la locale actuelle
  Locale get currentLocale => _languageController.value;

  /// Initialise le service de langue
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey) ?? 'fr';
      
      // Vérifier si la langue est supportée
      if (L10n.all.any((locale) => locale.languageCode == languageCode)) {
        _languageController.value = Locale(languageCode);
      } else {
        // Utiliser le français par défaut
        _languageController.value = const Locale('fr');
      }
      
      ErrorHandler.instance.logInfo(
        'Langue initialisée: ${_languageController.value.languageCode}',
        context: 'LanguageService.initialize',
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'LanguageService.initialize',
      );
      // Utiliser le français par défaut en cas d'erreur
      _languageController.value = const Locale('fr');
    }
  }

  /// Change la langue de l'application
  Future<void> setLanguage(String languageCode) async {
    try {
      // Vérifier si la langue est supportée
      if (!L10n.all.any((locale) => locale.languageCode == languageCode)) {
        throw ArgumentError('Langue non supportée: $languageCode');
      }
      
      // Sauvegarder la langue dans les préférences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      
      // Mettre à jour la langue
      _languageController.value = Locale(languageCode);
      
      ErrorHandler.instance.logInfo(
        'Langue changée: $languageCode',
        context: 'LanguageService.setLanguage',
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'LanguageService.setLanguage',
      );
      rethrow;
    }
  }

  /// Obtient le nom de la langue à partir du code
  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return 'Français';
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return 'Français';
    }
  }
}
