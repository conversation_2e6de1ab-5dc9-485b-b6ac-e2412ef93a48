{"appTitle": "SeaTrace", "loginTitle": "<PERSON><PERSON>", "email": "Email", "password": "Password", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password?", "serverIp": "Server IP Address", "testConnection": "Test Connection", "applyIp": "Apply", "discoverServers": "Discover Servers", "welcomeBack": "Welcome back, {name}", "@welcomeBack": {"placeholders": {"name": {"type": "Object"}}}, "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "notifications": "Notifications", "noNotifications": "No notifications", "markAllAsRead": "Mark all as read", "language": "Language", "french": "French", "arabic": "Arabic", "chooseLanguage": "Choose Language", "save": "Save", "cancel": "Cancel", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "loading": "Loading...", "retry": "Retry", "noData": "No data available", "scanFish": "<PERSON><PERSON>", "fishDetails": "Fish Details", "weight": "Weight", "temperature": "Temperature", "fishingMethod": "Fishing Method", "fishingZone": "Fishing Zone", "location": "Location", "selectMareyeur": "Select Mareyeur", "selectVeterinarian": "Select Veterinarian", "submit": "Submit", "pendingLots": "Pending Lots", "approvedLots": "Approved Lots", "rejectedLots": "Rejected Lots", "viewPendingLots": "View Pending Lots", "statistics": "Statistics", "recentActivity": "Recent Activity", "viewAll": "View All", "noRecentActivity": "No recent activity", "approve": "Approve", "reject": "Reject", "rejectionReason": "Rejection Reason", "initialPrice": "Initial Price", "minimalPrice": "Minimal Price", "startAuction": "Start Auction", "activeAuctions": "Active Auctions", "completedAuctions": "Completed Auctions", "availableAuctions": "Available Auctions", "myPurchases": "My Purchases", "bid": "Bid", "currentBid": "Current Bid", "timeRemaining": "Time Remaining", "extendAuction": "Extend Auction", "closeAuction": "Close Auction", "auctionClosed": "Auction Closed", "youWon": "You won this auction!", "confirmSpecies": "Confirm this species", "refreshCounters": "Refresh Counters", "countersRefreshed": "Counters refreshed successfully", "changeLanguage": "Change Language", "languageChanged": "Language changed successfully"}